This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

1

JetBGC: Joint Robust Embedding and Structural
Fusion Bipartite Graph Clustering
Liang <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Senior Member, IEEE, <PERSON><PERSON>, Senior Member, IEEE, <PERSON><PERSON>, Fellow, IEEE, and <PERSON><PERSON><PERSON>, Fellow, IEEE

Abstract— Bipartite graph clustering (BGC) has emerged as a
fast-growing research in the clustering community. Despite BGC
has achieved promising scalability, most variants still suffer from
the following concerns: a) Susceptibility to noisy features. They
construct bipartite graphs in the raw feature space, inducing
poor robustness to noisy features. b) Inflexible anchor selection
strategies. They usually select anchors through heuristic sampling
or constrained learning methods, degrading flexibility. c) Partial
structure mining. Existing methods are mainly built upon Linear
Reconstruction Paradigm (LRP) from subspace clustering or
Locally Linear Paradigm (LLP) from manifold learning, which
partially exploit linear or locally linear structures, lacking a
unified perspective to integrate global complementary structures.
To this end, we propose a novel model, termed Joint Robust
Embedding and Structural Fusion Bipartite Graph Clustering
(JetBGC), which focuses on three aspects, namely robustness,
flexibility, and complementarity. Concretely, we first introduce
a robust embedding learning module to extract latent representation that can reduce the impact of noisy features. Then, we
optimize anchors via a constraint-free strategy that can flexibly
capture data distribution. Furthermore, we revisit the consistency
and specificity of LRP and LLP, and design a new unified structural fusion strategy to integrate both linear and locally linear
structures from a global perspective. Therefore, JetBGC unifies
robust representation learning, flexible anchor optimization, and
structural bipartite graph fusion in a framework. Extensive
experiments on synthetic and real-world datasets validate our
effectiveness against existing baselines. The code is provided at
https://github.com/liliangnudt/JetBGC.
Index Terms—Latent embedding learning, structural fusion,
bipartite graph learning, multi-view clustering.

I. I NTRODUCTION

T

HE dramatic growth of data highlights the necessity
to develop unsupervised or self-supervised learning to

Liang Li, Junpu Zhang, Pei Zhang, and Xinwang Liu are with
School of Computer, National University of Defense Technology, Changsha
410073, China (e-mail: <EMAIL>; <EMAIL>;
<EMAIL>; <EMAIL>).
Jie Liu is with the Science and Technology and Parallel and Distributed
Processing Laboratory, National University of Defense Technology, Changsha
410073, China, and also with the Laboratory of Software Engineering for
Complex Systems, National University of Defense Technology, Changsha
410073, China (e-mail: <EMAIL>).
Yuangang Pan and Ivor W. Tsang are with the Center for Frontier AI Research, Agency for Science, Technology and Research (A*STAR), Singapore
138632 (e-mail: <EMAIL>; <EMAIL>)
Kenli Li is with the College of Computer Science and Electronic Engineering, Hunan University, Changsha 410073, China, and also with the
Supercomputing and Cloud Computing Institute, Hunan University, Changsha
410073, China (e-mail: <EMAIL>)
Keqin Li is with the Department of Computer Science, State University of
New York, New Paltz, NY 12561 USA (e-mail: <EMAIL>)
(Corresponding author: Xinwang Liu.)

reduce reliance on costly human annotations [1]. As a fundamental task in unsupervised learning, clustering plays a critical
role in uncovering the inherent grouping structures [2]–[6].
Owing to the flexibility of graph in representing complex
relationships [7]–[9], graph clustering has become an active
field of research [10]–[12]. To further exploit complementary
information from diverse sources or perspectives, multi-view
graph clustering (MVGC) [13], [14], as a popular subfield
of multi-view clustering (MVC) [15], [16], has been widely
applied in data mining [17], knowledge graph [18], and
computer vision [19].
However, existing MVGC methods require to compute pairwise similarities to build full graphs, which incurs quadratic
space and cubic time complexity w.r.t. instance number [20],
[21]. This limits their scalability when dealing with large-scale
data. In particular, computing and storing a similarity matrix
for over 100,000 nodes often results in out-of-memory errors
or unacceptable running time.
To improve scalability, multi-view bipartite graph clustering
(MVBGC) [22], [23] instead to merely build the memberships between a few representative anchors/landmarks and
all instances, achieving linear complexity. Fig. 1 plots two
popular paradigms for bipartite graph construction: subspace
clustering based Linear Reconstruction Paradigm (LRP) and
manifold learning based Locally Linear Paradigm (LLP). Built
upon LRP, Kang et al. [24] selected anchors via k-means
and concatenated view-specific bipartite graphs to fuse multiview structures. Sun et al. [25] incorporated anchor into optimization, avoiding sampling anchors. Wang et al. [26] further
extended a parameter-free version. Li et al. [27] designed
a feature self-attention mechanism to reduce noisy features.
Built upon LLP, Wang et al. [28] designed a semi-unsupervised
single-view model with constrained Laplacian rank, anchors
are selected via k-means. Li et al. [29] further extended [28]
into multi-view clustering scenarios, and learned a neighbor
bipartite graph. Nie et al. [30] and Chen et al. [31] introduced
feature re-weighting and selection methods to preserve useful
features. Li et al. [32] developed a multi-view bipartite graph
fusion framework, which introduced a heuristic anchor selection method and connectivity constraint, enforcing the bipartite
graph holds clear component structures. Lu et al. [33] further
designed a structure-diversity fusion to refine the graph.
Despite achieving favorable performance, existing MVBGC
models still encounter the following limitations: a) Susceptibility to noisy features: most methods estimate anchor-instance
correlations in the raw feature space, disregarding the impact
of noisy features. b) Inflexible anchor strategy: anchors are

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

2

TABLE I: Notations

Notation

Fig. 1: Sketches of existing LRP and LLP paradigms.

typically pre-selected via k-means or random sampling and
remain fixed thereafter. Although a learnable anchor strategy
has recently been proposed, it requires additional constraints,
limiting flexibility. c) Partial structure mining: most variants
are derived from LRP or LLP paradigms, capturing only linear
or locally linear structures, lacking a unified perspective to
explore global complementarity.
To this end, we consider designing a novel MVBGC model
that aims to enhance bipartite graph clustering from three aspects: robustness, flexibility, and complementarity. Concretely,
a) To improve robustness, we introduce a robust feature extraction module to learn robust embeddings from the input raw
feature space. b) To maintain flexibility, we propose to flexibly
acquire anchors through constraint-free optimization, unlike
the existing inflexible k-means, unstable random sampling, or
constrained learning methods. c) To achieve complementarity,
we generalize LRP and LLP into the latent space, and subtly
integrate them into a unified form to fuse linear and locally
linear structures.
We summarize our contributions as follows:
1) We develop a novel MVBGC model, named JetBGC,
which integrates robust embedding learning, constraintfree anchor optimization, and structural bipartite graph
fusion into a unified framework.
2) We revisit the consistency and specificity of two popular
BGC paradigms, and design a new structural bipartite
graph fusion strategy that integrates linear and locally
linear structures from a global perspective. Furthermore,
we establish a theoretical connection between our model
and the existing LLP paradigm by showing that the
proposed structural fusion strategy is a generalization
of LLP under a newly defined η-norm.
3) We design an ADMM solver with linear algorithm complexity w.r.t. instance number. Extensive experiments on
synthetic and real-world datasets verify the superiority.
Detailed ablation analysis validate the effectiveness of
the robust embedding learning, flexible anchor selection,
structural fusion modules.

k, n, υ
dp
d
m
µ, σ
ϵ
γ ∈ Rv×1
Xp ∈ Rdp ×n
Up ∈ Rdp ×d
V ∈ Rd×n
′
A′ ∈ Rd ×m
d×m
A∈R
Z ∈ Rn×m
Ep ∈ Rdp ×n
Λp ∈ Rdp ×n

Explanation
Number of clusters, samples, and views
Feature dimension for the p-th view
Latent feature dimension
Number of anchors
Penalty parameter, scaling factor
Number of neighbors in initialization
View weights
Input data for the p-th view
Base matrix for input data Xp
Consistent latent representation
Anchor matrix in raw feature space
Anchor matrix in latent space
Bipartite graph matrix
Auxiliary variables in ADMM
ALM multipliers

II. R ELATED R ESEARCH
A. Non-negative Matrix Factorization (NMF)
NMF [34] is a popular matrix decomposition method,
widely used in bioinformatics, image annotation, and social
e
networks. Given the raw data X ∈ Rd×n , NMF factorizes it
into two non-negative parts. Typically, the standard Frobeniusnorm (F-norm) form is as follows:
2

min ∥X − UV∥F , s.t. U ≥ 0, V ≥ 0,
U,V

(1)

where U is base matrix and V is coefficient matrix.
Following this idea, many variants are proposed. Ding et
al. [35] built the relationship between NMF and k-means
clustering. Cai et al. [36] introduced spectral embedding and
designed a graph regularization NMF. Kuang et al. [37]
proposed symmetric NMF that builds connection of NMF and
spectral clustering. Ding et al. [38] developed V-orthogonal
NMF to improve diversity as follows
2

min ∥X − UV∥F , s.t. U ≥ 0, V ≥ 0, VV⊤ = I.
U,V

(2)

Instead of standard F-norm, Kong et al. [39] proposed a
robust version [40] in which residuals are measured by ℓ2,1 norm, i.e.,
min ∥X − UV∥2,1 , s.t. U ≥ 0, V ≥ 0.
U,V

(3)

Based on ℓ2,1 -norm, Huang et al. [41] further designed a
robust graph regularization NMF. Li et al. [42] incorporated
linear discriminant analysis into NMF. For more details, please
refer to [43].
B. Bipartite Graph Construction
According to the construction manner, there are two representative strategies.
Linear Reconstruction Paradigm (LRP): LRP originates
from subspace clustering [44], which assumes that data can

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

3

5

0.15

5
2

Cluster 1
Cluster 2

0.1

3

2nd Latent Dim

3

-1

2nd Dim

4th Dim

2nd Dim

1

1

0

-1

-3

1

-1

-3

Cluster 1
Cluster 2
Anchors

Cluster 1
Cluster 2

-5
-12

-5
-12

-2

-8

-4

0

4

8

-2

-1

0

1st Dim

1

2

(a) Input clean features Xclean

-8

(b) Input noisy features Xnoisy

0
-0.05
-0.1

-4

0

4

-0.15
-0.15

8

1st Dim
(c) SFRF X&A′ (NMI: 21.51%)

3rd Dim

0.05

Cluster 1
Cluster 2
Anchors

-0.1

-0.05

0

0.05

0.1

1st Latent Dim

(d) JetBGC V&A (NMI: 100%)

Fig. 2: Robustness of the proposed latent embedding module to noisy
on a synthetic 4D single-view dataset
 features, evaluated

⊤

⊤
(SData-1) with two clusters (100 instances each). The input X = X⊤
∈ R4×200 contains clean features in the
clean , Xnoisy
first two dimensions and noisy features in the last two. (a)-(b) visualize the clean and noisy features, respectively. (c) shows
the results of SFRF, a JetBGC variant without latent embedding module, where anchors A′ ∈ R4×8 are learned in the raw
space. See Section III-H1 for details of SFRF. (d) shows the result of JetBGC, where features are projected into a 2D latent
space V ∈ R2×200 , yielding anchors A ∈ R2×8 that better capture the cluster structure.

be linearly reconstructed by anchors in the same subspace.
Formally, LRP is defined as:
min
Z

2

2

X − AZ⊤ F + λ ∥Z∥F ,

(4)

s.t. Z1 = 1, Z ≥ 0,
where A is the anchor matrix, Z is the bipartite graph matrix,
and λ is a hyper-parameter to balance the contribution of the
regularizer that avoids the trivial solution.
Since LRP paradigm models each instance as a combination of all anchors linearly through probability/similarity, the
resulting bipartite graph often shows a fuzzy representation,
as shown in Fig. 1 (left).
Locally Linear Paradigm (LLP): LLP builds upon the
manifold learning [45] that supposes that the original highdimensional data actually reside on the low-dimensional manifold. This setting enables it to preserve locally linear structures. Formally, LLP is expressed by
n X
m 

X
2
2
min
∥xi − aj ∥2 zij + λzij
,
Z
(5)
i=1 j=1
s.t. Z1 = 1, Z ≥ 0.
Typically, LLP measures similarity for anchor-instance pairs
through Euclidean distance, where longer distances correspond
to lower probabilities of being neighbors. As a result, the
constructed bipartite graph is often sparse, as shown in Fig. 1
(right).
By reviewing existing BGC research, we find that most
variants are built upon either LRP [24]–[26] or LLP [29], [30],
[32], which focus on modeling linear or locally linear structures while failing to exploit global complementary structures,
resulting in degraded discrimination of bipartite graphs.
III. METHODOLOGY
A. Probability Perspective for Bipartite Graph
To naturally motivate the subsequent model design, we
begin by introducing a probabilistic perspective of bipartite

graph [46] that how to recover instance-instance membership wij = p(xi 7→ xj ) with instance-anchor membership
zri = p(xi 7→ ar ) and zrj = p(ar 7→ xj ).
Specifically, the one-step transition probability from the i-th
instance (xi ) to the r-th anchor (ar ) is as follows
zri
p(1) (xi 7→ ar ) = Pm
,
r=1 zri
(6)
zrj
p(1) (ar 7→ xj ) = Pn
,
j=1 zrj
where 7→ denotes the transitioning.
The relationship between two instances can be viewed as
a double-step transition process, and the transition probability
from xi to xj is
p(2) (xi 7→ xj ) =

m
X

p(1) (xi 7→ ar ) p(1) (ar 7→ xj )

r=1
m
X

z z
Pnri rj .
=
j=1 zrj
r=1

(7)

Therefore, the instance-instance affinity matrix S ∈ Rn×n
in conventional graph clustering can be approximated via the
construction of bipartite graph Z ∈ Rn×m , where n ≫ m,
which significantly reduces computational and memory costs.
Anchors thus serve as representative points that capture the
underlying structural relationships among instances.
B. Robust Latent Embedding Learning
From the probabilistic perspective of bipartite graph, the
reliability of the double-step transition probability p(2) (xi 7→
xj ) depends on the quality of one-step transition probabilities
p(1) (xi 7→ ar ). However, the high-dimensional raw features
′
X ∈ Rd ×n may contain redundant or noisy features, which
induce unreliable one-step transition, and further degrades
double-step transition. To enhance robustness against noisy
and redundant features, we propose mapping the raw features
into a latent embedding space, built upon robust NMF [40]

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

2.5

4

2.5

4
Cluster 1
Cluster 2

2

3

Cluster 1
Cluster 2
Anchors

2.5
Cluster 1
Cluster 2
Anchors

2

2

Cluster 1
Cluster 2
Anchors

2

2nd Dim

0

1
0
-1
-2

-1

1

2nd Dim

1

2nd Dim

2nd Dim

1

0

-1

0

-1

-3

-2
-10

-5

0

5

-4
-15

10

-2
-10

-5

1st Dim

0

5

10

-2
-10

15

-5

(a) Input raw features X
0.15
Cluster 1
Cluster 2

0

5

10

(b) LRP X&A′ (NMI: 2.45%)

(c) LLP X&A′ (NMI: 22.37%)

0.8

0.15

0.6

0.1

-10

-5

1st Dim

1st Dim

Cluster 1
Cluster 2
Anchors

0

5

10

1st Dim

(d) SFRF X&A′ (NMI: 76.10% )
0.15

Cluster 1
Cluster 2
Anchors

0.1

0.1

Cluster 1
Cluster 2
Anchors

0.4

-0.05

2nd Dim

0

0.2
0
-0.2

0.05

2nd Dim

0.05

2nd Dim

2nd Dim

0.05

0

0

-0.05

-0.05

-0.1

-0.1

-0.4

-0.1
-0.6

-0.15
-0.7 -0.5 -0.3 -0.1 0.1 0.3 0.5 0.7

1st Dim

-0.8
-3

-0.15
-2

-1

0

1

2

3

-0.15
-0.7 -0.5 -0.3 -0.1 0.1 0.3 0.5 0.7

1st Dim

1st Dim

(e) Latent embedding V

(f) LRPE V&A (NMI: 68.38%)

(g) LLPE V&A (NMI: 87.82%)

-0.7 -0.5 -0.3 -0.1 0.1 0.3 0.5 0.7

1st Dim

(h) Proposed V&A (NMI: 100%)

Fig. 3: Comparison of two BGC paradigms on a synthetic 2D single-view dataset (SData-2). The input X ∈ R2×200 contains
two clusters, each with 100 instances. (a) Visualizes the input features. (b)-(d) show the clustering results of LRP, LLP, and
SFRF in the raw space. Section III-H1 for details of SFRF. (e) Displays the learnt latent embedding, and (f)-(g) present the
results of LRPE, LLPE, and the proposed JetBGC in the latent space.

in Eq. (3). For the multi-view setting, we jointly optimize
all views {Xp ∈ Rdp ×n }vp=1 and fuse them into a unified
embedding V ∈ Rd×n . Our Robust Latent Embedding
Learning (RLEL) is formulated as
υ
X
min
γp2 ∥Xp − Up V∥2,1 ,
v
{Up }p=1 ,V,γ

p=1

(
s.t.

(8)

VV⊤ = I;
γ ⊤ 1 = 1, γp ≥ 0, ∀p ∈ {1, 2, · · · , v},

where {Up ∈ Rdp ×d }vp=1 are the view-related base matrices,
γ measures the view importance.
Note that we introduce V-orthogonal constraint to enhance
the discrimination of the latent embedding. Moreover, we
remove the non-negative constraints on U and V, enabling it
to handle input data with mixed signs, rather than being limited
to non-negative data [47]. Empirical evidence supporting this
relaxation is provided in the supplementary material (Section
4.1). Theorem 1 further bridges the connection between our
RLEL and the relaxed multiple kernel k-means (MKKM) clustering. More details can be found in supplementary material
(Section 1).
Theorem 1: A reformulation of our RLEL model under the
Frobenius norm, i.e.,
υ
X
2
min
γp2 ∥Xp − Up V∥F ,
(9)
v
{Up }p=1 ,V,γ
⊤

p=1
⊤

s.t. VV = I; γ 1 = 1, γp ≥ 0, ∀p ∈ {1, 2, · · · , v},

is equivalent to MKKM clustering with a linear kernel, under
the condition that the latent feature dimension equals the
cluster number, i.e., d = k. Formally, this corresponds to:

max Tr H⊤ Kγ H ,
(10)
H,γ

s.t. H⊤ H = I; γ ⊤ 1 = 1, γp ≥ 0, ∀p ∈ {1, 2, · · · , v},
where
H denotes the consensus kernel partition, Kγ =
Pυ
2
p=1 γp Kp represents a linear combination of multiple base
kernels
via kernel function κγ (xi , xj ) = ψγ (xi )⊤ ψγ (xj ) =
Pυ
2
p=1 γp κp (xp(i) , xp(j) ), with ψ(·): x ∈ X 7→ H denoting
the mapping that transforms x into a kernel Hilbert space.
To demonstrate the robustness of RLEL module, we design
a synthetic dataset (SData-1), shown in Fig. 2. For a fair
comparison, all experimental settings for the compared SFRF
(which excludes the RLEL module) and JetBGC are the same.
For SFRF, the presence of noisy features lead to inaccurate
decision boundaries and degrade the performance, resulting in
a Normalized Mutual Information (NMI) of only 21.51%. In
contrast, our strategy demonstrates robustness to noisy features
and achieves competitive performance (NMI: 100%).
C. Flexible Anchor Learning
From the probabilistic perspective of bipartite graph, anchors act as intermediate “bridges” that connect the onestep transition probabilities p(1) (vi 7→ ar ) to the doublestep transition probability p(2) (vi 7→ vj ). Therefore, anchor
selection is critical.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

5

Conventional anchor selection strategies, such as random
sampling [48] and k-means clustering [49], typically pre-select
anchors before optimization, which lacks of flexibility and
reduces the reliability of p(1) (vi 7→ ar ), and consequently
undermines p(2) (vi 7→ vj ). Similar issues arise in constrained
anchor learning [26], where predefined constraints limit the
adaptability of anchors.
To address these limitations, we adopt a constraint-free
anchor learning strategy that removes the reliance on heuristic
methods or manual constraints. This enables the anchors to
better adapt to the underlying data distribution and more
reliable estimation of transition probabilities.
D. Structural Bipartite Graph Fusion
Given that LRP and LLP capture only linear or locally
linear structures, this section analyzes their consistency and
specificity.
By generalizing LRP into the embedding space (termed
LRPE), and further expanding it mathematically, we have:

Furthermore, we derive an alternative form of SFLE that
aligns closely with LLPE, demonstrating that SFLE is a
generalization of LLPE and establishing a connection between
the proposed module and the prior methods.
Concretely, by introducing the constant term Tr(VV⊤ ),
Eq. (13) can be reformulated as
n X
m
n
X
X
2
min
∥vi − aj ∥2 zij + λ
∥zi ∥2η ,
Z,A
(14)
i=1 j=1
i=1
s.t. Z1 = 1, Z ≥ 0,
where ∥·∥η denotes a newly defined vector norm in Theorem 2,
termd η-norm, which is induced by a positive definite (PD)
matrix I + ηA⊤ A with η > 0, namely

(15)
∥zi ∥2η = ⟨zi , zi ⟩η = zi I + ηA⊤ A z⊤
i .

Theorem 2: η-norm is a valid vector norm induced by a
positive definite matrix I + ηA⊤ A with η > 0.
Detailed proof is provided in supplementary material (Section 2). In experiments, we set η = λ1 to ensure that LRPE
and LLPE contribute equally to the overall objective.
2
2
V − AZ⊤ F + λ ∥Z∥F ,
(11)
Remark 1: The introduced η-norm is a generalization of
2
F-norm.
In Eq. (15), we enforce η > 0 to hold the positive
n
m
m
n
XX
X
X
2
definiteness of I + ηA⊤ A. Specifically, when η = 0, η-norm
zij
,
aj zij + λ
vi −
=
is simplified to F-norm. For LLP or LLPE variants, a Fi=1 j=1
j=1
i=1
2


norm based regularizer is typically incorporated to avoid trivial
solutions. Beyond this functionality, η-norm also contributes
2
⊤
⊤ 
⊤
⊤

+ λ∥Z∥F .
ZA
= Tr VV − 2VZA + AZ
{z
} | {z }
|
| {z }
to capturing linear structures by combining a LRPE-specific
Linear Specific
Common Part
Regularizer
term. Moreover, η-norm is compatible with existing LLP or
By generalizing LLP into the embedding space (termed LLPE variants. Incorporating it as a regularizer enables these
variants to extract both linear and locally linear structures.
LLPE), and further expanding it mathematically, we have:
To demonstrate the flexibility and complementarity of the
m
n


XX
2
2
∥vi − aj ∥2 zij + λzij
,
(12) proposed SFLE module, we design another synthetic dataset
(SData-2), as shown in Fig. 3. In all cases, the anchors are
i=1 j=1


learned via constraint-free optimization. The results show that:
(a) For LRP, anchors are scattered separately, reflecting its


⊤
⊤
2
⊤
=Tr VV − 2VZA +
ADm A
 + λ∥Z∥F . linear reconstruction property. (b) For LLP, anchors are primar|
{z
}
| {z }
| {z }
Common Part
Locally Linear Specific
Regularizer ily located within clusters, capturing locally linear structures.
(c) Fusing LRP and LLP combines their complementarity,
where Dm = diag(Z⊤ 1) ∈ Rm×m .
By reviewing Eq. (11) and Eq. (12), we find that they resulting in improved performance. (d) LRPE and LLPE
share a common part and a regularization term, while each inherit the structural properties of their respective backbones,
retains a specific part. This motivates us to integrate them while benefiting from the robust latent embedding module.
into a unified framework that captures global complementary (e) JetBGC achieves the highest performance, indicating the
structures. For simplicity, we combine their specific terms importance of learning latent embedding and modeling both
with equal weighting, along with the shared components, a linear and locally linear structures.
unified Structural Fusion on Latent Embedding (SFLE) can E. The Proposed JetBGC Model
be formulated as,
By integrating the above RLEL and SFLE modules, the


proposed JetBGC model is as follows,


⊤
⊤
υ
X
ADm A⊤
min Tr  −2VZA⊤ + AZ

| {zZA } +
| {z }
| {z }
A,Z
min
γp2 ∥Xp − Up V∥2,1 +
(16)
v
Common Part

+

λ∥Z∥2F

Linear Specific

Locally Linear Specific

, s.t. Z1 = 1, Z ≥ 0.

(13)

| {z }

Regularizer

Note that the constraint VV⊤ = I, thereby Tr(VV⊤ ) is
a constant term and can be omitted from the optimization.
Besides, we treat LRPE and LLPE as equally important, and
add their specific parts with equal contribution.

{Up }p=1 ,V,
A,Z,γ

p=1

|

{z

}

Robust Embedding Learning
⊤
⊤
⊤


Tr −2VZA + ηAZ ZA + ADm A⊤ + λ∥Z∥2F ,
|
{z
}
Structural Bipartite Graph Fusion

(
s.t.

Z1 = 1, Z ≥ 0; VV⊤ = I;
γ ⊤ 1 = 1, γp ≥ 0, ∀p ∈ {1, 2, · · · , v}.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

6

In summary, JetBGC integrates robust latent representation
learning, flexible anchor optimization, and structural bipartite
graph fusion, providing a unified solution for robustness,
flexibility, and complementarity.
F. Optimization
This section designs an ADMM solver. To separate constraints and simplify our model, we introduce υ auxiliary
variables {Ep = Xp − Up V}υp=1 . The Augmented Lagrange
Multiplier (ALM) function of Eq. (16) is expressed by
υ
X

min

{Up ,Ep ,Λp }v
p=1
p=1
V,Z,A,γ
⊤

γp2 ∥Ep ∥2,1 +
⊤

⊤

(17)
⊤



+ λ∥Z∥2F +

where Λp denotes the ALM multiplier to penalize the gap
between the original target and the auxiliary variables, and
µ is the ALM parameter. Eq. (17) can be solved by blockcoordinate descent method.
1) Update Up : Each Up is independently updated by
2

1
X p − Up V − E p + Λ p .
µ
F

Up

1
⊤ ⊤
Z[i,:] GZ⊤
[i,:] + r Z[i,:] ,
2
s.t. Z[i,:] 1 = 1, Z[i,:] ≥ 0,

min

Z[i,:]

Tr −2VZA + AZ ZA + ADm A
υ 

X
µ
2
⟨Λp , Xp − Up V − Ep ⟩ + ∥Xp − Up V − Ep ∥F
2
p=1
(
Z1 = 1, Z ≥ 0; VV⊤ = I;
s.t.
γ ⊤ 1 = 1, γp ≥ 0, ∀p ∈ {1, 2, . . . , v},

min

An undesirable case may arise when an anchor is not
connected to any instance, i.e., aj is an isolated anchor
without building correlations with all samples. In this case,
the corresponding diagonal entry δj of Dm = diag(Z⊤ 1)
becomes zero, inducing Dm is not a diagonal matrix. However,
such cases are not observed in experiments. Therefore, we
empirically assume that Ω−1 exists.
4) Update Z: Each Z[i,:] can be independently updated by
quadratic programming (QP) problem,


⊤
where G
= 2 A⊤ A + λI and r⊤ = diag A⊤ A
−

2V⊤ A [i,:] .
5) Update Ep : Each Ep is independently updated by
max
Ep

min

(19)

2) Update V: V is updated by
max Tr (V∆) , s.t. VV⊤ = I,
V

⊤

(20)

Pυ

⊤
p=1 Πp Up

where ∆ = 2ZA + µ
and Πp = Xp −
Ep + µ1 Λp . The problem can be solved by singular value
decomposition (SVD) [27].
3) Update A: A is updated by


min Tr A Z⊤ Z + Dm A⊤ − 2VZA⊤ .
(21)
A
By enforcing the partial derivative ∂(·)
∂A = 0, we have
A = VZ(Z⊤ Z + Dm )−1 .

(24)

6) Update γ: Each γp is independently updated by

γp

Since VV = I, the solution of Up is


1
Up = Xp − Ep + Λp V⊤ .
µ

γp2
1
∥Ep ∥2,1 + ∥Ep − Qp ∥2F ,
µ
2

where Qp = Xp − Up V + µ1 Λp . According to [50], the
solution is

( 
γ
γ
1 − µ∥qpi ∥2 qip , if µp < qip 2 ,
i
p
ep =
(25)
0,
otherwise.

(18)

⊤

(23)

υ
X

γp2 ξp , s.t. γ ⊤ 1 = 1, γp ≥ 0,

(26)

p=1

where ξp = ∥Xp − Up V∥2,1 . According to Cauchy-Schwarz
1/ξ

p
inequality, we have γp = Pυ 1/ξ
.
p
p=1
7) Update Λ and µ: ALM multiplier Λp and µ are updated
by

Λp =Λp + µ (Xp − Up V − Ep ) ,
µ =σµ,

(27)

where µ is the ALM penalty parameter used to update the
Lagrange multipliers, while σ is a scaling factor.
Algorithm 1 JetBGC
υ

1: Input: {Xp }p=1 , k, m, d, maximal iteration Γ.

υ
2: Initialize {Up }υ
p=1 , A, Z, V, γ, {Λp }p=1 .

(22)

Remark 2: Let Ω = Z⊤ Z + Dm , the inverse Ω−1 exists
if and only if Ω is positive definite (PD) matrix, i.e., all
⊤
eigenvalues {ωι > 0}m
ι=1 . It is easy to verify that Z Z is
a positive semi-definite (PSD) matrix and thus its eigenvalues
are greater than 0. For the diagonal matrix Dm = diag(Z⊤ 1),
its eigenvalues correspond to its diagonal elements {δι }m
ι=1 .
Ideally, if every anchor connects to at least one instance, then
Z⊤ 1 > 0, and Dm = diag(Z⊤ 1) is PD matrix. In this case,
Ω is the sum of a PSD and a PD matrix, ensuring Ω is PD
and thus invertible.

3: while not converged and iteration less than Γ do
4:
Update {Ep }υp=1 by solving Eq. (24).
5:
Update {Up }υp=1 by solving Eq. (18).
6:
Update A by solving Eq. (21).
7:
Update Z by solving Eq. (23).
8:
Update V by solving Eq. (20).
9:
Update γ by solving Eq. (26).
10:
Update {Λp }υp=1 by solving Eq. (27).
11: end while
12: Perform spectral decomposition on Z to get partition.
13: Output: The predicted labels.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

7

G. Initiation of Parameters
Initiation of Z and λ: Following a widely used strategy in
previous MVBGC works [28], [51], [52] that initializes Z by
retaining the top-ϵ nearest anchors for each instance (measured
by Euclidean distance), while setting all other connections to
zero. This method has been shown to (i) preserve sparsity of
Z and (ii) avoid the need for manually tuning the parameter
λ [51]–[53].
For brevity, we only present the solution, the detailed
derivations can refer to [52], [53]. In LLPE setting, Eq. (23)
can be reformulated as
2

1 ⊤
1
Z[i,:] +
, s.t. Z[i,:] 1 = 1, Z[i,:] ≥ 0. (28)
r
Z[i,:] 2
2λ
2
min

According to [27], the closed-form solution to Eq. (28) is
1 ⊤
Z[i,:] = max − 2λ
r + τi 1n , 0 , where τi can be solved by
Newton’s method.
Furthermore, by using the Lagrange multiplier technique
in [53], λ can be pre-determined by
λ=

ϵ
X

ϵ ⊤
1
r
−
r⊤ .
2 i,ϵ+1 2 j=1 i,j

(29)

where ϵ denotes the number of neighbors assigned to each
instance in initialization, we empirically set ϵ = 3 in experiments, which demonstrates favorable performance in most
cases. Further analysis on the sensitivity of ϵ is available in
the supplementary material (Section 4.2).
Initiation of other variables: Up is initialized to zero due
to unconstrained property; A is initialized as the centroids
obtained by applying k-means to the left singular vectors of
the concatenated multi-view data; V is initialized with the left
singular vectors of the concatenated multi-view data to satisfy
orthogonality constraint; Λp is initialized to zero; the penalty
parameter µ and the scaling parameter σ are initialized to 10
and 2, respectively. Further analysis on the sensitivity of µ and
σ are in supplementary material (Section 4.2-4.3).

2) Convergence: To solve the proposed optimization model
in Eq. (16), we develop an ADMM-based solver that adopts
block coordinate descent strategy [54]. The original objective
is decomposed into six subproblems, each of which has a
closed-form solution. As discussed in [55], [56], the scaling
parameter σ controls the update of ALM penalty µ. A larger
σ typically corresponds to fewer iterations to reach the convergence criterion, but may also induces precision loss of the
final objective value. Moreover, with increasing µ, the last
term in Eq. (17) approaches zero, thereby the ALM objective
asymptotically converges to the original function, which is
bounded by 0. According to previous convergence analyses of
ALM [39], [41], [57] and block coordinate descent [58], the
original function decreases monotonically during iteration and
converges to a local optimum. In experiments, the stopping
criterion is as follow,
|obj(iter − 1) − obj(iter)|
< 10−3
obj(iter − 1)
!

if (iter > 9) and

or iter > 30 or obj(iter) < 10−10

(31)

where iter is the iteration index, and obj(iter) denotes the
corresponding objective value.
3) Complexity Analysis: This section
Pυ analyses the complexities. For simplicity, we set ζ = p=1 dp .
Time Complexity: The time complexity consists of
nine parts. Updating {Ep }υp=1 requires O (nζd) time.
Updating {Up }υp=1 requires O (nζd) time. Updating
A requires O (nm(d + m)) time. Updating V requires
O n(ζ + dm + d2 + d + m) time. Updating Z requires
O (nm(dm + d)) time. Updating γ requires O (nζd) time.
Updating {Λp }υp=1 requires O (nζd)
 time. The total time
complexity is O n m2 d + d2 + ζd .
Space Complexity: Space complexity is mainly caused by
storing matrices, i.e., {Xp , Ep , Λp }υp=1 ∈ Rdp ×n , {Up }υp=1 ∈
Rdp ×d , V ∈ Rd×n , A ∈ Rd×m , Z ∈ Rn×m , The total space
complexity is O (n (ζ + d + m) + ζd + dm).
Therefore, the complexities are linear with n, making it can
scale to large-scale datasets with n ≥ 100, 000.

H. Analysis and Discussion
1) Structural Bipartite Graph Construction in Raw Feature
Space: If the bipartite graph is constructed in the input space,
JetBGC is reduced to the following form:
v
X

min
v

{Ap }p=1 ,Z,γ

⊤
⊤
γp2 Tr − 2Xp ZA⊤
p + ηAp Z ZAp

(30)

p=1


2
+ Ap D m A⊤
p + λ∥Z∥F ,
(
s.t.

Z1 = 1, Z ≥ 0;
γ ⊤ 1 = 1, γp ≥ 0, ∀p ∈ {1, . . . , v}.

We refer to the above model as Structural Fusion on
Raw Features (SFRF). The detailed optimization procedure is
provided in the supplementary material. Since it excludes the
RLEL module, SFRF shows less robustness to noisy features.

IV. E XPERIMENT
A. Synthetic Datasets
To visualize the effectiveness of JetBGC intuitively, we
design two single-view synthetic datasets.
SData-1: a 4D dataset shown in Fig. 2, consisting of two
clusters, each containing 100 samples, i.e., X ∈ R4×200 .
The first two dimensionnal features (1st and 2nd dimensions)
exhibit a two-moons shape, while the last two dimensions (the
3rd and 4th dimensions) are noisy features that follow Gaussian distributions. Specifically, the pink and green clusters are
distributed as C1 ∼ N (µ1 , Σ) and C2 ∼ C(µ2 , Σ), respectively, where µ1 = (0, 0)⊤ , µ2 = (0, 0)⊤ , Σ = diag(0.5, 0.5),
µ1 and µ2 are the mean vectors, and Σ represents the variance.
SData-2: a 2D dataset consist of two clusters, with each
cluster containing 100 samples, i.e., X ∈ R2×200 . Both
clusters follow Gaussian distributions. Specifically, the 1st

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

8

TABLE II: MVC Datasets
Datasets

View

Instance

Features

Cluster

WebKB cor
MSRCV1
Dermatology
ORLRnSp
ORL 4Views
Movies
Flower17
BDGP
VGGFace2 50
YouTubeFace10
EMNIST Digits

2
6
2
2
4
2
7
3
4
4
4

195
210
358
400
400
617
1,360
2,500
16,936
38,654
280,000

195/1,703
1,302/48/512/100/256/210
12/22
1,024/288
256/256/256/256
1,878/1,398
5,376/512/5,376/5,376/1,239/5,376/5,376
1,000/500/250
944/576/512/640
944/576/512/640
944/576/512/640

5
7
6
40
40
17
17
5
50
10
10

cluster (green) is distributed as C1 ∼ N (µ1 , Σ), while the
2nd cluster (pink) follows C2 ∼ N (µ2 , Σ). The mean vectors
for the two clusters are µ1 = (0, 1)⊤ , µ2 = (0, −1)⊤ . The
covariance matrix Σ = diag(s21 , s22 ), where s1 = 3.5 and
s2 = 0.4.
B. Real-world Datasets
Table II summarizes 11 real-world MVC datasets, with the
number of instances ranging from 195 to 286,000, the number
of clusters ranging from 5 to 50, and the feature dimensions
varying from 48 to 5,376. WebKB cor is a sub-network of
WebKB1 dataset which consists of web pages and hyperlinks,
including course, faculty, student, project, and staff categories.
MSRCV12 is a scene dataset which comprises 210 images
from 7 categories, including CENTRIST, CMT, GIST, HOG,
LBP, and DoG-SIFT. ORLRnSp and ORL 4views3 are face
datasets containing 400 images from 40 categories but with
different views. Movies4 involves 617 movies drawn from 17
categories, characterized by 2 views (actors and keywords).
Flower175 is a flower dataset with 17 categories and each
one contains 80 images. BDGP6 contains 2,500 images of
drosophila embryos from 5 classes. Each image is described
by a 1000D-lateral visual vector, a 500D dorsal visual vector,
and a 250D texture vector. VGGFace2 50 is extracted from
large-scale face recognition dataset VGGFace27 . YouTubeFace10 [59] is a face video datasets collected from YouTube.
EMNIST Digits8 is a subset of handwritten character digits
extracted from the NIST Special Database-199 , containing
280,000 characters with 10 balanced categories.
C. Compared Baselines
Thirteen state-of-the-art models are compared as baselines,
where MCLES [60], PMSC [61], FMR [62], AMGL [63],
and RMKM [64] are MVC methods with O(n3 ) computational complexity and O(n2 ) space complexity. BMVC [65],
1 https://starling.utdallas.edu/datasets/webkb/
2 https://www.microsoft.com/en-us/research/project/
image-understanding/downloads/
3 https://cam-orl.co.uk/facedatabase.html
4 https://lig-membres.imag.fr/grimal/data.html
5 https://www.robots.ox.ac.uk/ vgg/data/flowers/17/
6 https://www.fruitfly.org/
7 https://www.robots.ox.ac.uk/ vgg/data/vgg_face2/
8 https://www.nist.gov/itl/products-and-services/
emnist-dataset
9 https://www.nist.gov/srd/nist-special-database-19

LMVSC [24], SMVSC [25], FPMVS-CAG [26], SFMC [32],
FMCNOF [66], SDAFG [33], and MGSL [67] are BGC
models with O(n) time and space complexities. Source codes
are collected from public websites or authors’ homepage. The
hyper-parameters are tuned according to authors’ recommendations and we report the best metrics.
D. Experimental Setup
Following common experimental settings in clustering, the
cluster number k is known in advance [68]–[71]. For baselines
requiring k-means as a post-processing to generate discrete
clustering labels, we execute k-means 50 times repeatedly to
reduce randomness caused by stochastic centroid initialization,
and then report mean ± std. For JetBGC, the anchor number
m varies in [k, 2k, 3k], the latent feature dimension d varies
in [k, 2k, 3k, 4k], and d ≤ min{dp }υp=1 should be satisfied.
Five widely used metrics, namely Accuracy (ACC), Normalized Mutual Information (NMI), F-score, Adjusted Rand Index
(ARI), and Purity, are used to measure clustering performance
[72]–[74]. Experiments are obtained from a server with 12
Core Intel(R) i9 10900K CPUs @3.6GHZ, 64 GB RAM, and
Matlab 2020b.
E. Effectiveness
Table III reports clustering metrics, due to the space limitation, the results of MSGL are available in supplementary
material (Section 4.7). From the results, we find that:
1) JetBGC achieves competitive clustering performance,
whose ACC outperforms the second best with large
margins of 12.32%, 10.10%, 10.65%, 0.79%, 4.05%,
2.86%, 15.38%, 8.96%, 2.99%, 4.91%, and 10.42% on
eleven datasets, respectively. On average, our model
outperforms competitors with 7.59%, 7.95%, 5.22%,
7.69%, and 9.55% improvements of ACC, NMI, Fscore, ARI, and Purity, respectively, fully validating our
effectiveness.
2) MCLES, PMSC, FMR,
 AMGL, and RMKM are MVGC
models with O n3 time complexity and O n2 space
complexity, these baselines cannot scale to large-scale
datasets (n ≥ 100, 000) and easily incur “OOM” error,
greatly limiting their applications. Two MVBGC baselines, SFMC and SMVSC, also suffer unavailable results
“-” on EMNIST Digits, due to unacceptable extremely
long running time caused by complex optimization.
3) Most compared baselines construct graphs directly from
the input data rather than latent embedding, making
them sensitive to noisy or redundant features, thereby
degrading the quality of graph. In addition, they are typically built upon either the LRP or LLP paradigm. These
limitations can explain their degraded performance.
F. Comparison with Inflexible Anchor Selection
This section validates the “flexibility” of constrained-free
anchor optimization. We denote constrained-free anchor selection strategy as “Flexible”, while the baseline that select

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

9

TABLE III: Comparison of clustering metrics. The best metrics are marked in bold, the second-best ones are italicized and
underlined, “OOM” is out-of-memory error, and “-” means unavailable results caused by extremely long running time.
Datasets

MCLES

PMSC

FMR

AMGL

RMKM

BMVC

LMVSC

SMVSC

FPMVS-CAG

SFMC

FMCNOF

SDAFG

Proposed

37.58±4.55
70.51±4.98
78.64±5.41
69.60±2.82
47.76±2.36
25.57±1.01
27.13±0.84
37.22±2.03
13.36±0.60
72.93±3.96
6.2

44.71±1.80
71.95±5.36
82.96±7.44
70.88±1.47
54.63±1.49
25.76±0.05
25.99±1.83
32.62±1.17
12.06±0.36
67.09±5.70
62.24±4.08
4.2

44.10±0.00
60.48±0.00
49.44±0.00
37.25±0.00
37.00±0.00
11.51±0.00
7.57±0.00
14.32±0.00
3.64±0.00
55.80±0.00
10.1

42.05±0.00
47.14±0.00
62.01±0.00
19.50±0.00
21.50±0.00
17.02±0.00
17.43±0.00
31.08±0.00
5.51±0.00
43.42±0.00
36.50±0.00
9.6

44.10±0.00
70.95±0.00
56.70±0.00
61.50±0.00
57.75±0.00
9.08±0.00
8.68±0.00
20.16±0.00
3.58±0.00
64.48±0.00
61.54±0.00
7.9

57.83±1.88
93.84±7.55
93.61±5.87
71.67±2.69
65.55±2.64
32.50±1.37
52.50±1.94
58.48±0.03
16.35±0.41
79.79±5.51
79.40±2.62
1.0

9.52±2.38
62.01±2.61
66.62±2.66
84.84±0.79
72.73±1.13
23.21±1.10
25.78±0.76
9.85±0.71
16.21±0.49
78.57±2.80
6.4

14.49±1.94
65.69±3.27
71.90±5.05
84.75±0.41
77.41±0.54
25.07±0.19
25.81±1.59
10.02±1.33
14.74±0.55
76.11±3.06
53.47±2.44
5.0

5.08±0.00
60.23±0.00
38.68±0.00
76.42±0.00
76.30±0.00
28.72±0.00
7.87±0.00
26.23±0.00
1.63±0.00
77.46±0.00
8.1

13.36±0.00
38.42±0.00
54.24±0.00
39.50±0.00
43.32±0.00
12.42±0.00
14.68±0.00
10.29±0.00
4.74±0.00
39.15±0.00
28.36±0.00
9.4

5.08±0.00
76.23±0.00
51.61±0.00
80.51±0.00
76.89±0.00
5.72±0.00
5.64±0.00
0.32±0.00
2.01±0.00
71.97±0.00
72.73±0.00
8.3

38.88±1.58
92.62±4.94
89.69±2.62
86.57±1.01
81.77±1.02
31.91±0.96
49.57±0.94
35.70±0.05
19.25±0.35
84.48±2.38
72.77±0.99
1.0

31.11±3.30
59.31±2.82
70.06±3.60
56.80±2.18
32.37±1.71
15.65±0.57
17.53±0.21
28.81±0.38
6.35±0.18
68.34±5.78
6.3

36.93±0.97
61.55±3.54
77.37±6.23
56.61±1.22
42.87±1.47
16.24±0.07
17.29±0.39
28.79±0.58
6.10±0.07
66.10±5.06
49.29±3.01
4.8

42.89±0.00
52.43±0.00
42.90±0.00
19.44±0.00
23.74±0.00
10.92±0.00
10.94±0.00
25.09±0.00
4.16±0.00
61.25±0.00
9.2

35.94±0.00
33.85±0.00
57.89±0.00
8.57±0.00
12.09±0.00
13.94±0.00
13.93±0.00
28.89±0.00
4.36±0.00
32.88±0.00
25.64±0.00
8.7

42.89±0.00
63.58±0.00
53.89±0.00
27.98±0.00
23.11±0.00
11.48±0.00
11.02±0.00
33.27±0.00
4.15±0.00
52.40±0.00
57.93±0.00
7.1

49.76±1.79
91.64±7.36
91.93±5.04
62.31±2.85
53.20±2.63
20.48±1.17
36.03±1.05
46.29±0.03
8.37±0.20
78.70±4.53
70.30±1.41
1.0

6.51±4.67
52.28±3.58
62.79±4.82
55.61±2.27
30.13±1.81
9.20±0.84
10.57±0.26
6.65±0.37
3.77±0.22
63.93±7.01
5.9

12.25±2.08
54.55±4.63
71.54±8.52
55.43±1.26
41.20±1.54
9.26±0.10
9.86±0.53
5.69±0.85
3.10±0.09
61.13±6.13
43.13±3.77
4.7

1.35±0.00
42.25±0.00
17.22±0.00
17.06±0.00
21.86±0.00
-0.01±0.00
0.01±0.00
0.49±0.00
0.01±0.00
55.95±0.00
10.5

11.21±0.00
21.60±0.00
43.75±0.00
4.91±0.00
8.60±0.00
4.31±0.00
5.26±0.00
6.01±0.00
0.65±0.00
22.54±0.00
16.13±0.00
8.9

1.35±0.00
55.70±0.00
40.43±0.00
25.27±0.00
20.14±0.00
0.01±0.00
0.26±0.00
0.00±0.00
0.01±0.00
44.44±0.00
51.83±0.00
9.3

33.95±2.35
90.24±8.65
89.97±6.18
61.32±2.94
51.96±2.73
15.20±1.24
31.85±1.11
32.62±0.04
6.39±0.21
76.07±5.13
66.93±1.59
1.0

47.71±3.76
71.51±4.02
80.35±3.73
73.29±2.42
51.91±2.16
27.90±1.42
27.88±0.78
37.80±1.22
13.90±0.58
77.35±4.61
6.3

49.42±1.43
72.33±5.01
83.55±6.84
74.60±1.42
58.97±1.39
28.83±0.10
26.38±1.85
34.82±1.23
12.27±0.36
69.43±5.88
62.30±4.04
5.2

44.62±0.00
62.86±0.00
50.00±0.00
80.25±0.00
78.00±0.00
28.53±0.00
10.29±0.00
56.60±0.00
3.86±0.00
74.10±0.00
6.9

49.23±0.00
50.48±0.00
62.85±0.00
21.25±0.00
21.75±0.00
17.83±0.00
17.57±0.00
33.32±0.00
5.67±0.00
46.53±0.00
36.90±0.00
9.6

44.62±0.00
70.95±0.00
66.48±0.00
67.50±0.00
63.00±0.00
10.53±0.00
9.19±0.00
20.16±0.00
3.74±0.00
69.50±0.00
66.12±0.00
8.4

68.62±1.57
94.52±6.17
94.89±2.15
75.03±2.35
68.95±2.06
35.39±1.03
54.13±1.76
60.65±0.03
17.23±0.35
85.33±3.30
79.99±1.80
1.2

ACC (%)
WebKB cor
MSRCV1
Dermatology
ORLRnSp
ORL 4Views
Movies
Flower17
BDGP
VGGFace2 50
YouTubeFace10
EMNIST Digits
Avg Rank

37.92±2.52
60.86±3.45
49.67±4.72
31.64±1.72
29.18±1.99
29.65±1.34
OM
OM
OM
OM
OM
10.6

45.51±3.96
47.45±4.23
80.75±4.46
31.36±1.60
21.48±1.02
21.70±0.82
20.82±0.74
27.86±0.00
OM
OM
OM
9.2

42.28±2.07
77.48±6.40
81.72±5.66
47.62±2.58
25.21±1.10
22.49±1.04
33.43±1.75
41.84±0.03
OM
OM
OM
7.0

27.13±1.75
76.44±6.30
22.57±0.59
63.55±3.11
59.73±2.78
11.66±0.54
9.70±1.53
34.48±2.23
2.95±0.35
OM
OM
8.5

43.08±0.00
71.43±0.00
74.86±0.00
54.75±0.00
47.00±0.00
17.99±0.00
23.24±0.00
41.36±0.00
8.23±0.00
74.88±0.00
OM
6.4

43.59±0.00
26.67±0.00
63.97±0.00
46.25±0.00
43.25±0.00
20.58±0.00
26.99±0.00
39.76±0.00
10.30±0.00
58.58±0.00
68.99±0.00
7.0

44.50±1.77
83.73±7.20
79.02±6.63
60.37±2.28
61.50±2.96
26.45±1.59
37.12±1.86
49.52±2.39
10.56±0.26
74.48±5.30
61.75±4.05
3.4

WebKB cor
MSRCV1
Dermatology
ORLRnSp
ORL 4Views
Movies
Flower17
BDGP
VGGFace2 50
YouTubeFace10
EMNIST Digits
Avg Rank

4.94±0.62
51.72±2.37
41.67±3.43
57.91±1.60
54.03±1.67
29.76±1.02
OM
OM
OM
OM
OM
10.9

20.13±1.04
34.29±2.81
85.11±1.91
56.34±1.30
43.87±0.84
20.03±0.68
19.13±0.48
4.52±0.00
OM
OM
OM
9.2

22.39±1.50
69.48±3.31
79.97±3.67
68.86±1.52
48.33±0.81
19.58±0.98
30.65±0.91
12.57±0.05
OM
OM
OM
7.2

6.78±1.59
77.65±3.23
3.20±0.57
83.03±1.44
80.28±1.37
12.14±0.66
10.25±4.01
17.21±3.46
2.04±0.50
OM
OM
7.5

14.84±0.00
63.03±0.00
71.10±0.00
74.35±0.00
71.83±0.00
14.92±0.00
22.07±0.00
16.98±0.00
9.66±0.00
78.83±0.00
OM
6.4

6.59±0.00
8.29±0.00
60.79±0.00
66.94±0.00
65.32±0.00
18.19±0.00
25.62±0.00
15.74±0.00
13.48±0.00
54.66±0.00
70.08±0.00
7.8

16.61±1.04
78.93±4.60
70.17±3.94
78.93±0.93
79.39±1.15
25.94±1.10
35.37±1.10
25.85±1.92
12.64±0.28
77.74±2.03
61.87±2.47
3.9

WebKB cor
MSRCV1
Dermatology
ORLRnSp
ORL 4Views
Movies
Flower17
BDGP
VGGFace2 50
YouTubeFace10
EMNIST Digits
Avg Rank

37.14±2.83
48.53±2.10
42.79±2.67
20.64±1.50
17.63±1.35
16.85±0.70
OM
OM
OM
OM
OM
10.2

36.45±2.61
34.05±2.34
83.50±4.24
16.83±1.38
6.48±0.54
13.27±0.40
12.33±0.36
28.59±0.00
OM
OM
OM
9.6

34.38±1.30
66.76±4.50
77.59±5.15
33.01±2.50
9.27±0.83
11.12±0.47
20.09±0.81
29.21±0.04
OM
OM
OM
7.8

28.81±2.08
70.28±4.42
18.46±0.78
40.03±6.27
35.12±4.54
10.48±0.08
11.49±0.55
34.79±1.04
3.91±0.02
OM
OM
8.1

33.82±0.00
59.98±0.00
74.82±0.00
39.54±0.00
33.66±0.00
9.81±0.00
14.35±0.00
30.25±0.00
3.69±0.00
66.93±0.00
OM
7.4

40.32±0.00
16.01±0.00
56.62±0.00
28.86±0.00
24.84±0.00
9.93±0.00
16.61±0.00
32.40±0.00
5.10±0.00
52.53±0.00
61.38±0.00
7.0

36.10±1.37
77.43±6.43
70.50±4.17
49.26±2.30
50.10±2.86
15.06±0.90
23.99±0.95
38.51±1.04
5.09±0.15
68.93±3.19
54.07±3.51
3.8

WebKB cor
MSRCV1
Dermatology
ORLRnSp
ORL 4Views
Movies
Flower17
BDGP
VGGFace2 50
YouTubeFace10
EMNIST Digits
Avg Rank

3.03±2.41
38.66±2.82
21.54±4.29
17.83±1.60
14.66±1.46
10.81±0.85
OM
OM
OM
OM
OM
10.5

14.72±1.53
22.74±2.83
79.17±5.33
14.44±1.45
3.81±0.55
5.07±0.57
6.70±0.36
3.26±0.00
OM
OM
OM
8.9

12.39±1.30
61.19±5.41
72.16±6.44
31.38±2.57
7.06±0.84
4.99±0.51
15.09±0.86
11.26±0.05
OM
OM
OM
6.9

0.59±0.58
64.81±5.50
0.20±0.17
38.02±6.61
32.88±4.80
0.11±0.05
0.72±0.71
7.90±2.26
0.05±0.05
OM
OM
8.3

10.27±0.00
53.33±0.00
68.33±0.00
37.92±0.00
31.74±0.00
1.98±0.00
8.83±0.00
11.61±0.00
1.59±0.00
62.80±0.00
OM
6.2

7.78±0.00
2.26±0.00
45.02±0.00
27.06±0.00
22.88±0.00
4.00±0.00
11.06±0.00
12.59±0.00
2.98±0.00
46.36±0.00
56.85±0.00
6.7

13.55±1.67
73.60±7.66
63.25±5.63
47.96±2.38
48.82±2.96
9.09±1.05
19.18±1.02
22.82±1.85
3.06±0.16
64.86±3.75
48.70±3.97
3.3

WebKB cor
MSRCV1
Dermatology
ORLRnSp
ORL 4Views
Movies
Flower17
BDGP
VGGFace2 50
YouTubeFace10
EMNIST Digits
Avg Rank

43.15±0.36
61.57±2.91
54.59±3.51
33.78±1.64
31.96±1.78
32.92±1.05
OM
OM
OM
OM
OM
10.9

54.05±1.59
49.91±3.78
85.37±1.89
34.39±1.58
23.90±1.08
24.69±1.24
22.20±0.62
30.31±0.00
OM
OM
OM
9.1

51.33±1.48
79.01±4.16
84.79±2.87
50.97±2.45
26.48±1.14
24.84±1.14
34.74±1.38
42.36±0.06
OM
OM
OM
6.8

27.59±1.91
80.45±4.29
23.12±0.50
70.35±2.44
66.92±2.07
12.07±0.52
10.76±1.53
35.75±2.67
3.17±0.37
OM
OM
8.6

49.23±0.00
74.76±0.00
75.70±0.00
57.75±0.00
53.00±0.00
19.29±0.00
24.49±0.00
42.12±0.00
9.28±0.00
79.70±0.00
OM
6.4

43.59±0.00
27.14±0.00
65.08±0.00
49.25±0.00
47.50±0.00
23.01±0.00
29.41±0.00
40.52±0.00
11.44±0.00
63.87±0.00
71.38±0.00
7.7

49.26±1.39
85.25±5.56
80.97±4.29
63.96±1.89
65.68±2.53
29.36±1.44
38.81±1.54
49.64±1.90
11.41±0.25
78.39±3.43
65.08±2.93
3.9

NMI (%)

F-score (%)

ARI (%)

Purity (%)

anchors via k-means is denoted as “Inflexible”. For a fair
comparison, all experimental settings are kept consistent.
Fig. 4 visualizes the evolution of the normalized affinity
⊤
graph (ZD−1
m Z ) on MSRCV1. It is worth noting that the “Inflexible” method achieves its best performance with m = 1k
and d = 2k, while the “Flexible” method performs best with
m = 2k and d = 3k, thereby their initialized graphs are different. Moreover, the “Inflexible” method achieves a 14.57%
improvement in ACC through optimization, with most of the
gains occurring in the first iteration. However, in subsequent
iterations, the reinforcement of the graph is inconspicuous.
Differently, the “Flexible” method progressively improves

the performance over iterations. It gradually reduces intercluster noisy similarities, refines block-diagonal structures, and
achieves a 35.12% improvement in ACC. The visualization
results demonstrate the effectiveness of our unconstrained
anchor optimization strategy.
Fig. 5 further quantifies clustering metrics. The “Flexible”
method consistently outperforms the “Inflexible” manner by
large margins with an average of 4.79%, 5.29%, 5.41%,
6.47%, and 5.22% improvements respecting ACC, NMI,
F-score, ARI, and Purity, respectively. The improvements
demonstrate the superiority of the flexible learnable anchor
strategy.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

10-3
5.5

MSRCV1 (ACC: 72.38%)

1

5.4

30

10

MSRCV1 (ACC: 85.13%)

1

MSRCV1 (ACC: 85.96%)

1

MSRCV1 (ACC: 87.03%)

1

MSRCV1 (ACC: 86.95%)

1

0.07

0.07

0.045
30

30

0.06 30

0.035 60

0.05 60

0.04

30

0.06

0.06

5.3
60

60

5.2
90

5.1
5

120

4.9

150

0.03

4.7
210

0.04 90

0.04 90

0.04

120

0.02 120

0.03120

0.03120

0.03

150

0.015 150

0.02150

0.02150

0.02

180

0.01180

0.01180

0.01

210

210

210

0.025

0.01
180

0.005

210

1

30

60

90

120

150

180

210

1

30

(a) Inflexible Initialization

60

90

120

150

180

1

210

(b) Inflexible 1st-Iter
10-3

MSRCV1 (ACC: 58.72%)

1

10-3
5.6

MSRCV1 (ACC: 74.84%)

1

30

60

90

120

150

180

210

30

5.4

5.05
60

60

10-3

MSRCV1 (ACC: 76.9%)

1

4.85

150

60

90

120

150

180

210

210

1

30

60

90

120

150

180

210

(e) Inflexible 10th-Iter
MSRCV1 (ACC: 93.84%)

1
0.02

30

0.035
0.03
0.025

90

90

0.02

120

0.01 120

0.015

4.5 150

150

0.01

180

0.005

60

6.5
6
5.5

0.015

5
150

4.6

4

180
210

1

30

(f) Flexible Initialization

60

90

120

150

180

0.005

180

3.5

4.4
210

30

180

60

7

4.8
150

4.75
1

150

30

7.5

120

180

210

120

MSRCV1 (ACC: 89.03%)

1

30

90

4.8
180

90

5

4.9 120

120

60

8

5.2

4.95 90

30

(d) Inflexible 6th-Iter

60

5
90

1

(c) Inflexible 3rd-Iter

5.1
30

0.05

90

90

4.8
180

60

0.05

210

210

1

(g) Flexible 1st-Iter

30

60

90

120

150

180

210

210
1

30

(h) Flexible 3rd-Iter

60

90

120

150

180

210

1

(i) Flexible 6th-Iter

30

60

90

120

150

180

210

(j) Flexible 10th-Iter

Fig. 4: Evolution of the normalized affinity matrix over iterations on MSRCV1.
100

70
Inflexible

90

Flexible

Inflexible

Flexible

90
Inflexible

Flexible

Inflexible

Flexible

60
90

80

30

Metrics (%)

Metrics (%)

Metrics (%)

40

80

70

20

Metrics (%)

80

50

70

70

60

60
60

50

10
0

ACC

NMI

F-score

ARI

50

Purity

ACC

(a) WebKB cor

NMI

F-score

ARI

50

Purity

(b) MSRCV1

40

NMI

F-score

ARI

40

Purity

Inflexible

Flexible

F-score

ARI

Purity

90
Inflexible

Flexible

Inflexible

Flexible

85

20
30

NMI

(d) ORL 4views

25

Flexible

ACC

(c) ORLRnSp

60
Inflexible

ACC

50

40

Metrics (%)

20

Metrics (%)

Metrics (%)

Metrics (%)

80
15

10

75

70
10

30
5

0

ACC

NMI

F-score

ARI

20

Purity

ACC

(e) Movies

NMI

F-score

ARI

Purity

(f) Flower17

0

65

ACC

NMI

F-score

ARI

Purity

60

(g) VGGFace2 50

ACC

NMI

F-score

ARI

Purity

(h) YouTubeFace10

Fig. 5: Clustering performance: flexible vs. inflexible anchor selection.
G. Ablation Study
TABLE IV: Experimental settings of ablation analysis
Model

Embedding

Linear

Locally linear

SFRF

−

✓

✓

LRPE

✓

−

✓

LLPE

✓

✓

−

Proposed

✓

✓

✓

This section validates the “complementarity” by comparing
JetBGC with LRPE, and LLPE backbones. For comparison,
we also report the results of SFRF and “Inflexible” baseline.
Table IV gives the experimental settings.
Fig. 6 visualizes the normalized affinity graph on MSRCV1
and Flower17, and Fig. 7 presents a comparison of clustering
metrics. We observe that:
1) LRPE, derived from self-expressive subspace clustering,
constructs correlations between each instance and all
anchors. As a result, the graph shows a fuzzy represen-

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

MSRCV1 (ACC: 79.95%)

1

MSRCV1 (ACC: 81.22%)

1

0.35

30

30

0.3

60

0.25 60

90

0.2

120

0.15120

150

0.1 150

11

10-3
7.5

1

7

30

6.5

MSRCV1 (ACC: 84.96%)

0.035

0.06 30

0.03

60

0.05 60

0.025

90

0.04 90

0.02

0.02 120

0.03120

0.015

150

0.02150

0.01

180

0.01180

0.035
0.03

6

90

MSRCV1 (ACC: 93.84%)

1

0.07

30

0.04

60

MSRCV1 (ACC: 86.95%)

1

0.045

90

0.025

5.5
120

5

0.015

150

4.5
0.05180

180

0.01
180

4

210

210
1

30

60

90

120

150

180

30

(a) MSRCV1 SFRF

60

90

120

150

180

210

1

(b) MSRCV1 LRPE

Flower17 (ACC: 32.84%)

1

210

210

1

210

0.005
60

90

120

150

180

10

-4

0.025 200

Flower17 (ACC: 47.21%)

1

400

0.02

9.5
9

400

8

0.015

7.5

800

800
0.01

0.005
1200

6

1

200

400

600

800

1000

150

180

210

1

30

90

120

150

180

210

Flower17 (ACC: 52.5%)

1

0.04

0.07
200

200

0.035

400

0.03

0.06
0.05

0.025
600

0.025
600

0.04

0.02
800

60

(e) MSRCV1 Ours

400

600
0.02

800

0.03 800

1000

0.01 1000

0.021000

1200

0.0051200

0.011200

0.015

5.5 1360

1360

1360

120

0.03
400

6.5
1200

90

0.015

7
1000

1000

60

Flower17 (ACC: 42.8%)

1

0.035

200

8.5
600

600

30

(d) MSRCV1 Inflexible

10
200

0.005

210

1

210

(c) MSRCV1 LLPE

Flower17 (ACC: 49.98%)

1

30

1

1200 1360

(f) Flower17 SFRF

200

400

600

800

1000

1200 1360

(g) Flower17 LRPE

200

400

600

800

1000

1200 1360

0.005

1360

1360

1

0.01

1

(h) Flower17 LLPE

200

400

600

800

1000

1

1200 1360

(i) Flower17 Inflexible

200

400

600

800

1000

1200 1360

(j) Flower17 Ours

Fig. 6: Visualization of normalized affinity graph of SFRF, LRPE, LLPE, and our SFLE methods.

100

60

90
SFRF

LRPE

LLPE

Proposed

SFRF

LRPE

LLPE

Proposed

65
SFRF

LRPE

LLPE

Proposed

21
SFRF

LRPE

LLPE

Proposed

SFRF

LRPE

LLPE

Proposed

18
50
80

70

40

30

Metrics (%)

70

Metrics (%)

80

15

Metrics (%)

Metrics (%)

Metrics (%)

90

55

45

35

12
9
6

60
20

25

10

15

3
60

ACC

NMI

F-score

ARI

(a) MSRCV1

Purity

50

ACC

NMI

F-score

ARI

(b) ORLRnSp

Purity

ACC

NMI

F-score

ARI

Purity

ACC

(c) Flower17

NMI

F-score

ARI

Purity

0

(d) BDGP

ACC

NMI

F-score

ARI

Purity

(e) VGGFace2 50

Fig. 7: Performance comparison of LRPE, LLPE, SFRF, and the proposed model.

tation, with many noisy inter-cluster similarities, which
degrade the block-diagonal structure and the quality of
clustering.
2) LLPE, grounded in manifold learning, emphasizes locality by connecting each instance to a few neighbor anchors. Therefore, the graphs is more sparser and contains
fewer noisy connections. However, several dominant
noisy similarities may mislead clustering partition.
3) SFLE integrates the properties of LRPE and LLPE,
achieving a more discriminative graph with promising
metrics. Compared to LRPE, JetBGC exploits clearer
block-diagonal structures, while compared to LLPE,
JetBGC reduces the noisy similarities.
4) Compared to SFRF, which constructs graphs from raw
features, JetBGC introduces a robust embedding module
for feature extraction, which reduces the negative impact
of the noisy features.
5) JetBGC outperforms the compared baselines with competitive performance on almost all datasets.
These results are convincing evidence to verify the overall
superiority of JetBGC.

H. Convergence
Fig. 8 empirically validates the “convergence” of JetBGC.
As discussed in our theoretical analysis, with the increase
of ALM parameter µ, the ALM objective function gradually
converges to the original function, which is bounded by 0. In
experiments, we find that the objective decreases and stabilizes
within ten iterations, verifying convergence. More convergence
results are available in supplementary material (Section 10).
V. C ONCLUSION
This paper proposes a novel bipartite graph clustering
model, JetBGC, which focuses on three aspects: robustness,
flexibility, and complementarity. To improve robustness, we
derive a new feature extractor that learns robust latent embedding, which reduces the adverse impact of noisy features.
To achieve flexibility, we design a constraint-free anchor optimization strategy instead of following the existing fixed anchor
or constrained learnable methods. To enhance complementarity, we bride the connection of two popular BGC paradigms,
and design a novel structural bipartite graph fusion strategy
from a unified perspective, to integrate global complementary

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

104

9439

1.344
1.3435
1.343

9438

1.3425

9437

1.342

2

4

6

8

Number of Iteration

(a) Flower17

10

1.0408
1.0407
1.0406
1.0405

1.0402
2

4

6

Number of Iteration

(b) BDGP

8

10

2.4204
2.4202

2

4

6

Number of Iteration

(c) VGGFace2 50

8

10

1.7236
1.7234

1.723

2.4196
0

1.7238

1.7232

2.4198

1.0403

0

1.724

2.4206

2.42

1.0404

1.3415

9436

Objective Value
1.7242

2.4208

Objective Value

9440

Objective Value

Objective Value

Objective Value

9441

106

1.7244

Objective Value
2.421

1.0409

1.3445

9442

105

2.4212

Objective Value
1.041

1.345

0

105

1.0411

Objective Value

Objective Value
9443

Objective Value

1.3455

9444

12

1.7228
0

2

4

6

8

Number of Iteration

(d) YouTubeFace10

10

0

2

4

6

8

10

Number of Iteration

(e) EMNIST Digits

Fig. 8: Experimental validation of the convergence.
structures. Overall, JetBGC integrates robust embedding learning, constraint-free anchor optimization, and structural bipartite graph fusion into a unified framework. This paper provides
new insights into enhancing bipartite graph clustering that will
inspire more variants in the BGC community. One limitation
of JetBGC is its reliance on post-processing to generate the
discrete clustering labels, which may introduce variance in the
performance. Alternative strategies, such as Laplacian rank
constraint [53] or one-pass clustering method [75], provide
potential solutions for directly generating labels, which will
be our future research. Another limitation is its assumption of
complete multi-view data. However, in many scenarios, missing data is common due to sensor failures or data corruption.
Tackling incomplete multi-view data remains a challenging yet
practical problem, and we leave this in subsequent research.
ACKNOWLEDGMENT
This work was supported in part by National Natural Science Foundation of China under Project 62325604, 62441618,
62276271, and in part by National Key Research and Development Program of China under Grant 2021YFB0300101, and
in part by China Scholarship Council No. ************.
R EFERENCES
[1] Y. LeCun, Y. Bengio, and G. Hinton, “Deep learning,” nature, vol. 521,
no. 7553, pp. 436–444, 2015.
[2] S. Huang, I. W. Tsang, Z. Xu, and J. Lv, “Latent representation guided
multi-view clustering,” IEEE Trans. Knowl. Data Eng., pp. 1–6, 2022.
[3] J. Xu, Y. Ren, H. Tang, Z. Yang, L. Pan, Y. Yang, X. Pu, P. S. Yu, and
L. He, “Self-supervised discriminative feature learning for deep multiview clustering,” IEEE Trans. Knowl. Data Eng., pp. 1–12, 2022.
[4] C. Liu, J. Wen, Y. Xu, B. Zhang, L. Nie, and M. Zhang, “Reliable
representation learning for incomplete multi-view missing multi-label
classification,” IEEE Trans. Pattern Anal. Mach. Intell., pp. 1–17, 2025.
[5] Y. Liu, K. Liang, J. Xia, S. Zhou, X. Yang, X. Liu, and S. Z. Li, “Dinknet: Neural clustering on large graphs,” in Proc. of the 40th Int. Conf.
Mach. Learn. (ICML), 2023, Honolulu, Hawaii, USA, vol. 202, 2023,
pp. 21 794–21 812.
[6] W. Tu, S. Zhou, X. Liu, Z. Cai, Y. Zhao, Y. Liu, and K. He, “WAGE:
Weight-Sharing Attribute-Missing Graph Autoencoder,” IEEE Trans.
Pattern Anal. Mach. Intell., pp. 1–18, 2025.
[7] Y. Wang, Y. Tong, C. Long, P. Xu, K. Xu, and W. Lv, “Adaptive dynamic
bipartite graph matching: A reinforcement learning approach,” in Proc.
of the 35-th IEEE Int. Conf. Data. Min. (ICDE), Macao, China, 2019,
pp. 1478–1489.
[8] Y. Gao, T. Zhang, L. Qiu, Q. Linghu, and G. Chen, “Time-respecting
flow graph pattern matching on temporal graphs,” IEEE Trans. Knowl.
Data Eng., vol. 33, no. 10, pp. 3453–3467, 2021.

[9] Y. Liu, W. Tu, S. Zhou, X. Liu, L. Song, X. Yang, and E. Zhu, “Deep
graph clustering via dual correlation reduction,” in Proc. of the 36-th
AAAI Conf. Artif. Intell., Virtual Event, vol. 36, no. 7, 2022, pp. 7603–
7611.
[10] H. Li, Y. Feng, C. Xia, and J. Cao, “Overlapping graph clustering in
attributed networks via generalized cluster potential game,” ACM Trans.
Knowl. Discov. Data, vol. 18, no. 1, pp. 27:1–27:26, 2024.
[11] H. Zhong, J. Wu, C. Chen, J. Huang, M. Deng, L. Nie, Z. Lin, and X.S. Hua, “Graph contrastive clustering,” in Proc. of the IEEE/CVF Conf.
Comput. Vis. Pattern Recog. (CVPR), 2021, pp. 9224–9233.
[12] Y. Liu, S. Zhou, X. Yang, X. Liu, W. Tu, L. Li, X. Xu, and F. Sun,
“Improved dual correlation reduction network with affinity recovery,”
IEEE Trans. Neural Netw. Learn. Syst., vol. 36, no. 4, pp. 6159–6173,
2025.
[13] E. Pan and Z. Kang, “Multi-view contrastive graph clustering,” Proc.
Adv. Neural Inf. Process. Syst. (NeurIPS), vol. 34, pp. 2148–2159, 2021.
[14] Z. Lin, Z. Kang, L. Zhang, and L. Tian, “Multi-view attributed graph
clustering,” IEEE Trans. Knowl. Data Eng., vol. 35, no. 2, pp. 1872–
1880, 2023.
[15] C. Liu, J. Wen, Z. Wu, X. Luo, C. Huang, and Y. Xu, “Information
recovery-driven deep incomplete multiview clustering network,” IEEE
Trans. Neural Netw. Learn. Syst., vol. 35, no. 11, pp. 15 442–15 452,
2024.
[16] U. Fang, M. Li, J. Li, L. Gao, T. Jia, and Y. Zhang, “A comprehensive
survey on multi-view clustering,” IEEE Trans. Knowl. Data Eng.,
vol. 35, no. 12, pp. 12 350–12 368, 2023.
[17] Y. Liu, S. Zhu, J. Xia, Y. Ma, J. Ma, X. Liu, S. Yu, K. Zhang,
and W. Zhong, “End-to-end learnable clustering for intent learning in
recommendation,” vol. 37, 2024, pp. 5913–5949.
[18] H. Xiao, Y. Chen, and X. Shi, “Knowledge graph embedding based
on multi-view clustering framework,” IEEE Trans. Knowl. Data Eng.,
vol. 33, no. 2, pp. 585–596, 2021.
[19] W. Zhang, L. Jiao, F. Liu, S. Yang, and J. Liu, “Adaptive contourlet
fusion clustering for sar image change detection,” IEEE Trans. Image
Process., vol. 31, pp. 2295–2308, 2022.
[20] S. Huang, I. Tsang, Z. Xu, and J. C. Lv, “Measuring diversity in graph
learning: A unified framework for structured multi-view clustering,”
IEEE Trans. Knowl. Data Eng., vol. 34, no. 12, pp. 5869–5883, 2022.
[21] F. Nie, W. Chang, Z. Hu, and X. Li, “Robust subspace clustering with
low-rank structure constraint,” IEEE Trans. Knowl. Data Eng., vol. 34,
no. 3, pp. 1404–1415, 2022.
[22] S. Shi, F. Nie, R. Wang, and X. Li, “Fast multi-view clustering via
prototype graph,” IEEE Trans. Knowl. Data Eng., vol. 35, no. 1, pp.
443–455, 2023.
[23] L. Li, Y. Pan, J. Liu, Y. Liu, X. Liu, K. Li, I. W. Tsang, and K. Li,
“Bgae: Auto-encoding multi-view bipartite graph clustering,” IEEE
Trans. Knowl. Data Eng., vol. 36, no. 8, pp. 3682–3696, 2024.
[24] Z. Kang, W. Zhou, Z. Zhao, J. Shao, M. Han, and Z. Xu, “Large-scale
multi-view subspace clustering in linear time,” in Proc. of the 34-th
Conf. Artif. Intell., New York, NY, USA, 2020, pp. 4412–4419.
[25] M. Sun, P. Zhang, S. Wang, S. Zhou, W. Tu, X. Liu, E. Zhu, and
C. Wang, “Scalable multi-view subspace clustering with unified anchors,” in Proc. of the 29-th ACM Int. Conf. Multimedia, Virtual Event,
China, 2021, pp. 3528–3536.
[26] S. Wang, X. Liu, X. Zhu, P. Zhang, Y. Zhang, F. Gao, and E. Zhu, “Fast
parameter-free multi-view subspace clustering with consensus anchor
guidance,” IEEE Trans. Image Process., vol. 31, pp. 556–568, 2022.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

[27] L. Li, J. Zhang, S. Wang, X. Liu, K. Li, and K. Li, “Multi-view bipartite
graph clustering with coupled noisy feature filter,” IEEE Trans. Knowl.
Data Eng., vol. 35, no. 12, pp. 12 842–12 854, 2023.
[28] Z. Wang, L. Zhang, R. Wang, F. Nie, and X. Li, “Semi-supervised
learning via bipartite graph construction with adaptive neighbors,” IEEE
Trans. Knowl. Data Eng., vol. 35, no. 5, pp. 5257–5268, 2023.
[29] L. Li and H. He, “Bipartite graph based multi-view clustering,” IEEE
Trans. Knowl. Data Eng., vol. 34, no. 7, pp. 3111–3125, 2022.
[30] F. Nie, X. Dong, L. Tian, R. Wang, and X. Li, “Unsupervised feature
selection with constrained ℓ2,0 -norm and optimized graph,” IEEE Trans.
Neural Networks Learn. Syst., vol. 33, no. 4, pp. 1702–1713, 2020.
[31] H. Chen, F. Nie, R. Wang, and X. Li, “Fast unsupervised feature
selection with bipartite graph and ℓ2,0 -norm constraint,” IEEE Trans.
Knowl. Data Eng., vol. 35, no. 5, pp. 4781–4793, 2023.
[32] X. Li, H. Zhang, R. Wang, and F. Nie, “Multiview clustering: A scalable
and parameter-free bipartite graph fusion method,” IEEE Trans. Pattern
Anal. Mach. Intell., vol. 44, no. 1, pp. 330–344, 2022.
[33] X. Lu and S. Feng, “Structure diversity-induced anchor graph fusion
for multi-view clustering,” ACM Trans. Knowl. Discov. Data., vol. 17,
no. 2, pp. 17:1–17:18, 2023.
[34] D. D. Lee and H. S. Seung, “Learning the parts of objects by nonnegative matrix factorization,” nature, vol. 401, no. 6755, pp. 788–791,
1999.
[35] C. Ding, X. He, and H. D. Simon, “On the equivalence of nonnegative
matrix factorization and spectral clustering,” in Proc. of 2005 SIAM Int.
Conf. Data. Min. SIAM, 2005, pp. 606–610.
[36] D. Cai, X. He, J. Han, and T. S. Huang, “Graph regularized nonnegative
matrix factorization for data representation,” IEEE Trans. Pattern Anal.
Mach. Intell., vol. 33, no. 8, pp. 1548–1560, 2010.
[37] D. Kuang, C. Ding, and H. Park, “Symmetric nonnegative matrix
factorization for graph clustering,” in Proc. of the 2012 SIAM Int. Conf.
Data. Min. SIAM, 2012, pp. 106–117.
[38] C. H. Q. Ding, T. Li, W. Peng, and H. Park, “Orthogonal nonnegative
matrix t-factorizations for clustering,” in Proc. of the 12-th ACM
SIGKDD Int. Conf. on Knowl. Discov. Data. Min., Philadelphia, PA,
USA, 2006, pp. 126–135.
[39] D. Kong, C. H. Q. Ding, and H. Huang, “Robust nonnegative matrix
factorization using ℓ2,1 -norm,” in Proc. of the 20th ACM Int. Conf. Inf.
Knowl. Manag. (CIKM), Glasgow, United Kingdom, 2011, pp. 673–682.
[40] C. H. Q. Ding, D. Zhou, X. He, and H. Zha, “r1 -pca: rotational invariant
ℓ1 -norm principal component analysis for robust subspace factorization,”
in Proc. of the 23-th Int. Conf. Mach. Learn. (ICML), Pittsburgh,
Pennsylvania, USA, vol. 148, 2006, pp. 281–288.
[41] J. Huang, F. Nie, H. Huang, and C. Ding, “Robust manifold nonnegative
matrix factorization,” ACM Trans. Knowl. Discov. Data., vol. 8, no. 3,
pp. 1–21, 2014.
[42] X. Li, M. Chen, and Q. Wang, “Discrimination-aware projected matrix
factorization,” IEEE Trans. Knowl. Data Eng., vol. 32, no. 4, pp. 809–
814, 2019.
[43] T. Li and C.-c. Ding, “Nonnegative matrix factorizations for clustering:
A survey,” Data Clustering, pp. 149–176, 2018.
[44] E. Elhamifar and R. Vidal, “Sparse subspace clustering: Algorithm,
theory, and applications,” IEEE Trans. Pattern Anal. Mach. Intell.,
vol. 35, no. 11, pp. 2765–2781, 2013.
[45] L. K. Saul and S. T. Roweis, “Think globally, fit locally: unsupervised
learning of low dimensional manifolds,” J. Mach. Learn. Res., vol. 4,
no. Jun, pp. 119–155, 2003.
[46] H. Zhang, J. Shi, R. Zhang, and X. Li, “Non-graph data clustering via
O(n) bipartite graph convolution,” IEEE Trans. Pattern Anal. Mach.
Intell., vol. 45, no. 7, pp. 8729–8742, 2022.
[47] C. H. Ding, T. Li, and M. I. Jordan, “Convex and semi-nonnegative
matrix factorizations,” IEEE Trans. Pattern Anal. Mach. Intell., vol. 32,
no. 1, pp. 45–55, 2008.
[48] D. Cai and X. Chen, “Large scale spectral clustering via landmark-based
sparse representation,” IEEE Trans. Cybern., vol. 45, no. 8, pp. 1669–
1680, 2015.
[49] Y. Li, F. Nie, H. Huang, and J. Huang, “Large-scale multi-view spectral
clustering via bipartite graph,” in Proc. of the 39-th AAAI Conf. Artif.
Intell., Austin, Texas, USA, B. Bonet and S. Koenig, Eds., 2015, pp.
2750–2756.
[50] M. Yuan and Y. Lin, “Model selection and estimation in regression with
grouped variables,” J. R. Stat. Soc. Series. B. (Stat. Methodol.), vol. 68,
no. 1, pp. 49–67, 2006.
[51] X. Yang, G. Lin, Y. Liu, F. Nie, and L. Lin, “Fast spectral embedded
clustering based on structured graph learning for large-scale hyperspectral image,” IEEE Geosci. Remote Sens. Lett. and Remote Sensing
Letters, vol. 19, pp. 1–5, 2022.

13

[52] J. Wang, L. Wang, F. Nie, and X. Li, “Fast unsupervised projection for
large-scale data,” IEEE Trans. Neural Netw. Learn. Syst., vol. 33, no. 8,
pp. 3634–3644, 2021.
[53] F. Nie, X. Wang, M. I. Jordan, and H. Huang, “The constrained laplacian
rank algorithm for graph-based clustering,” in Proc. of the 30-th AAAI
Conf. Artif. Intell., Phoenix, Arizona, USA, 2016, pp. 1969–1976.
[54] S. J. Wright, “Coordinate descent algorithms,” Math. Program., vol. 151,
no. 1, pp. 3–34, 2015.
[55] S. Boyd, N. Parikh, E. Chu, B. Peleato, J. Eckstein et al., “Distributed
optimization and statistical learning via the alternating direction method
of multipliers,” Found. Trends Mach. Learn., vol. 3, no. 1, pp. 1–122,
2011.
[56] T. Goldstein, B. O’Donoghue, S. Setzer, and R. Baraniuk, “Fast alternating direction optimization methods,” SIAM J. Imaging Sci., vol. 7,
no. 3, pp. 1588–1623, 2014.
[57] Z. Lin, M. Chen, and Y. Ma, “The augmented lagrange multiplier method
for exact recovery of corrupted low-rank matrices,” arXiv preprint
arXiv:1009.5055, 2010.
[58] P. Tseng, “Convergence of a block coordinate descent method for
nondifferentiable minimization,” J. Optim. Theory. Appl., vol. 109, no. 3,
p. 475, 2001.
[59] D. Huang, C. Wang, and J. Lai, “Fast multi-view clustering via ensembles: Towards scalability, superiority, and simplicity,” IEEE Trans.
Knowl. Data Eng., vol. 35, no. 11, pp. 11 388–11 402, 2023.
[60] M. Chen, L. Huang, C. Wang, and D. Huang, “Multi-view clustering in
latent embedding space,” in Proc. of the 34-th AAAI Conf. Artif. Intell.,
New York, NY, USA, 2020, pp. 3513–3520.
[61] Z. Kang, X. Zhao, C. Peng, H. Zhu, J. T. Zhou, X. Peng, W. Chen, and
Z. Xu, “Partition level multiview subspace clustering,” Neural Netw.,
vol. 122, pp. 279–288, 2020.
[62] R. Li, C. Zhang, Q. Hu, P. Zhu, and Z. Wang, “Flexible multi-view
representation learning for subspace clustering,” in Proc. of the 28-th
IJCAI Int. Jt. Conf. Artif. Intell., Macao, China, 2019, pp. 2916–2922.
[63] F. Nie, J. Li, and X. Li, “Parameter-free auto-weighted multiple graph
learning: A framework for multiview clustering and semi-supervised
classification,” in Proc. of the 25-th IJCAI Int. Jt. Conf. Artif. Intell.,
New York, NY, USA, 2016, pp. 1881–1887.
[64] X. Cai, F. Nie, and H. Huang, “Multi-view k-means clustering on big
data,” in Proc. of the 23-th IJCAI Int. Jt. Conf. Artif. Intell., Beijing,
China, 2013, pp. 2598–2604.
[65] Z. Zhang, L. Liu, F. Shen, H. T. Shen, and L. Shao, “Binary multi-view
clustering,” IEEE Trans. Pattern Anal. Mach. Intell., vol. 41, no. 7, pp.
1774–1782, 2019.
[66] B. Yang, X. Zhang, F. Nie, F. Wang, W. Yu, and R. Wang, “Fast multiview clustering via nonnegative and orthogonal factorization,” IEEE
Trans. Image Process., vol. 30, pp. 2575–2586, 2020.
[67] Z. Kang, Z. Lin, X. Zhu, and W. Xu, “Structured graph learning for
scalable subspace clustering: From single view to multiview,” IEEE
Trans. Cybern., vol. 52, no. 9, pp. 8976–8986, 2021.
[68] Y. Liu, X. Yang, S. Zhou, X. Liu, S. Wang, K. Liang, W. Tu, and L. Li,
“Simple contrastive graph clustering,” IEEE Trans. Neural Netw. Learn.
Syst., vol. 35, no. 10, pp. 13 789–13 800, 2024.
[69] C. Liu, Z. Wu, J. Wen, Y. Xu, and C. Huang, “Localized sparse
incomplete multi-view clustering,” IEEE Trans. Multimedia, vol. 25, pp.
5539–5551, 2023.
[70] Y. Liu, X. Yang, S. Zhou, X. Liu, Z. Wang, K. Liang, W. Tu, L. Li,
J. Duan, and C. Chen, “Hard sample aware network for contrastive
deep graph clustering,” in Proc. of the 37-th AAAI Conf. Artif. Intell.,
Washington DC, USA, vol. 37, no. 7, 2023, pp. 8914–8922.
[71] W. Tu, R. Guan, S. Zhou, C. Ma, X. Peng, Z. Cai, Z. Liu, J. Cheng,
and X. Liu, “Attribute-missing graph clustering network,” in Proc. of the
38-th AAAI Conf. Artif. Intell., Vancouver, Canada, 2024, pp. 15 392–
15 401.
[72] J. Zhang, L. Li, P. Zhang, Y. Liu, S. Wang, C. Zhou, X. Liu, and
E. Zhu, “TFMKC: Tuning-Free Multiple Kernel Clustering Coupled
With Diverse Partition Fusion,” IEEE Trans. Neural Netw. Learn. Syst.,
pp. 1–14, 2024.
[73] J. Zhang, L. Li, S. Wang, J. Liu, Y. Liu, X. Liu, and E. Zhu, “Multiple
kernel clustering with dual noise minimization,” in Proc. of the 30-th
ACM Int. Conf. Multimedia, Lisboa, Portugal, New York, NY, USA,
2022, p. 3440–3450.
[74] L. Li, S. Wang, X. Liu, E. Zhu, L. Shen, K. Li, and K. Li, “Local
sample-weighted multiple kernel clustering with consensus discriminative graph,” IEEE Trans. Neural Networks, vol. 35, no. 2, pp. 1721–1734,
2024.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

This article has been accepted for publication in IEEE Transactions on Knowledge and Data Engineering. This is the author's version which has not been fully edited and
content may change prior to final publication. Citation information: DOI 10.1109/TKDE.2025.3583718

JOURNAL OF LATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021

[75] X. Liu, L. Liu, Q. Liao, S. Wang, Y. Zhang, W. Tu, C. Tang, J. Liu,
and E. Zhu, “One pass late fusion multi-view clustering,” in Proc. of
the 38th Int. Conf. Mach. Learn. (ICML), Virtual Event, vol. 139, 2021,
pp. 6850–6859.

Liang Li received the bachelor’s degree from
Huazhong University of Science and Technology
(HUST), Wuhan, China, in 2018, and the master’s
degree from the National University of Defense
Technology (NUDT), Changsha, China, in 2020,
where he is currently pursuing the Ph.D. degree
since 2021. His current research interests include
graph learning, AI4Science, ranking.

Yuangang Pan received the Ph.D. degree in computer science from the University of Technology
Sydney (UTS), Ultimo, NSW, Australia, in 2020. He
is working as a Research Scientist at the A*STAR
Centre for Frontier AI Research, Singapore. He
has authored or coauthored articles in various top
journals, such as JMLR, TPAMI, TNNLS, TKDE,
and TOIS. His research interests include deep clustering, deep generative learning, and robust ranking
aggregation.

Junpu Zhang received the bachelor’s degree from
the Ocean University of China, Qingdao, China, in
2020. He is pursuing the master degree with the
National University of Defense Technology, Changsha, China. His current research interests include
kernel learning, ensemble learning, and multi-view
clustering.

Pei Zhang received the bachelor’s degree from
Yunnan University, China, in 2018 and the master’s degree in from National University of Defense
Technology (NUDT), China, in 2020, where she
is currently pursuing the Ph.D. degree. Her current research interests include incomplete multi-view
clustering, and deep clustering.

Jie Liu received the Ph.D. degrees in computer
science from the National University of Defense
Technology (NUDT), Changsha, China. He is a
professor with the College of Computer Science
and Technology, National University of Defense
Technology. His research interests include high performance computing and machine learning.

14

Kenli Li (Senior Member, IEEE) received the Ph.D.
degree from Huazhong University of Science and
Technology (HUST), China, in 2003. He is currently
a full professor of computer science and technology
at Hunan University and director of the National
Supercomputing Center in Changsha. His research
interests include parallel and distributed computing,
high-performance computing, AI, cloud computing,
and big data. He has published over 600 research
papers, such as TC, TPDS, TCC, TKDE, DAC,
AAAI, ICPP, etc.

Ivor W. Tsang (Fellow, IEEE) is the Director of
A*STAR Centre for Frontier AI Research, Singapore. He is a Professor of artificial intelligence with
the University of Technology Sydney (UTS), Australia, and the Research Director of the Australian
Artificial Intelligence Institute (AAII). His research
interests include transfer learning, deep generative
models, learning with weakly supervision, Big Data
analytics for data with extremely high dimensions in
features, samples and labels.
Dr. Tsang was the recipient of the ARC Future
Fellowship for his outstanding research on Big Data analytics and large-scale
machine learning, in 2013. In 2019, his JMLR article toward ultrahigh dimensional feature selection for Big Data was the recipient of the International
Consortium of Chinese Mathematicians Best Paper Award. In 2020, he was
recognized as the AI 2000 AAAI/IJCAI Most Influential Scholar in Australia
for his outstanding contributions to the field between 2009 and 2019. He
serves as the Editorial Board for JMLR, MLJ, JAIR, TPAMI, TAI, TBD, and
TETCI. He serves/served as an AC or Senior AC for NeurIPS, ICML, AAAI,
and IJCAI, and the steering committee of ACML.

Keqin Li (Fellow, IEEE) received a B.S. degree
in computer science from Tsinghua University in
1985 and a Ph.D. degree in computer science from
the University of Houston in 1990. He is currently
a SUNY Distinguished Professor with the State
University of New York and a National Distinguished Professor with Hunan University (China).
He has authored or co-authored more than 950
journal articles, book chapters, and refereed conference papers. He received several best paper awards
from international conferences including PDPTA1996, NAECON-1997, IPDPS-2000, ISPA-2016, NPC-2019, ISPA-2019, and
CPSCom-2022. He holds nearly 70 patents announced or authorized by
the Chinese National Intellectual Property Administration. He is among the
world’s top five most influential scientists in parallel and distributed computing
in terms of single-year and career-long impacts based on a composite indicator
of the Scopus citation database. He was a 2017 recipient of the Albert
Nelson Marquis Lifetime Achievement Award for being listed in Marquis
Who’s Who in Science and Engineering, Who’s Who in America, Who’s
Who in the World, and Who’s Who in American Education for more than
twenty consecutive years. He received the Distinguished Alumnus Award from
the Computer Science Department at the University of Houston in 2018.
He received the IEEE TCCLD Research Impact Award from the IEEE CS
Technical Committee on Cloud Computing in 2022 and the IEEE TCSVC
Research Innovation Award from the IEEE CS Technical Community on
Services Computing in 2023. He is a Member of the SUNY Distinguished
Academy. He is an AAAS Fellow, an IEEE Fellow, and an AAIA Fellow.
He is a Member of Academia Europaea (Academician of the Academy of
Europe).

Xinwang Liu (Senior Member, IEEE) received
the Ph.D. degree from the National University of
Defense Technology (NUDT), Changsha, China, in
2013. He is currently a Full Professor with the College of Computer Science and Technology, NUDT.
His current research interests include kernel learning
and unsupervised feature learning. He has published
over 100 peer-reviewed papers, such as TPAMI,
TKDE, TIP, TNNLS, TMM, TIFS, ICML, NeurIPS,
ICCV, CVPR, AAAI, and IJCAI. He serves as an
Associated Editor of the TNNLS and TCYB.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 30,2025 at 14:19:29 UTC from IEEE Xplore. Restrictions apply.
© 2025 IEEE. All rights reserved, including rights for text and data mining and training of artificial intelligence and similar technologies. Personal use is permitted,
but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

