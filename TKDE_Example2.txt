IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, VOL. 37, NO. 8, AUGUST 2025

4497

DASCE: Long-Tailed Data Augmentation Based
Sparse Class-Correlation Exploitation
Mengnan <PERSON> , Student Member, IEEE, <PERSON><PERSON><PERSON> , Member, IEEE, <PERSON><PERSON><PERSON> , <PERSON>, IEEE,
<PERSON>, Member, IEEE, <PERSON><PERSON><PERSON>, Senior Member, IEEE, <PERSON><PERSON>ao , Fellow, IEEE,
and <PERSON><PERSON> , Senior Member, IEEE

Abstract—The long-tailed data distribution frequently occurs
in the real-world scenarios, whereas deep learning is not effective
enough for such distribution. In order to improve the effectiveness
for the long-tailed data, data augmentation is widely used to balance
the distribution of classes by generating new samples. However,
most existing studies are designed from the perspective of the
class-independence assumption by default, ignoring the effect of
interrelation among classes for data augmentation, which causes
that some generated samples may be unrepresentative and useless
for balancing the class-distribution. Inspired by this, we propose a
new data augmentation method based the sparse class-correlation
exploitation in this paper, which can generate more representative
samples by utilizing the class-correlation, to effectively balance the
class-distribution for the long-tailed data. In the proposed method,
a sparse class-correlation exploration module is first proposed to explore the potential correlations among multiple classes for boosting
the classification performance. Based on the class-correlations, the
pivotal seed-samples are generated by maximizing the sparse representation of challenging samples. Meanwhile, an ambiguity-filtered
translation module is designed to generate more representative new
samples for the target classes based the obtained seed-samples by
enhancing the class-consistency and suppressing the deviation from
the target classes. In addition, we introduce the self-supervised
feature and fuse it with the discriminative feature to explore more
accurate class-correlations. Experimental results illustrate that the
proposed method obtains better performance only with a small
number of generated samples than the state-of-the-art methods.
Index Terms—Long-tailed data distribution, deep learning, data
augmentation, sparse class-correlation exploitation.

I. INTRODUCTION
ONG-TAILED data distribution represents the most prevalent case of imbalanced data [1], in which the size of samples belonging to each class decreases significantly from large

L

Received 19 June 2024; revised 22 March 2025; accepted 10 May 2025. Date
of publication 2 June 2025; date of current version 7 July 2025. This work was
supported in part by the State Key Program of National Natural Science of China
under Grant 62234010, in part by the National Natural Science Foundation of
China under Grant 61806154 and Grant 62102296, and in part by the Shaanxi
Province Natural Science Basic Research Program under Grant 2023-JC-YB560. Recommended for acceptance by A. Asudeh. (Corresponding authors:
Shasha Mao; Yimeng Zhang.)
Mengnan Qi, Yimeng Zhang, and Yuming Zhang are with
Faculty of Integrated Circuit, Xidian University, Xi’an 710071,
China (e-mail: <EMAIL>; <EMAIL>;
<EMAIL>).
Shasha Mao, Jing Gu, Shuiping Gou, and Licheng Jiao are with the
Key Laboratory of Intelligent Perception and Image Understanding of Ministry of Education, School of Artificial Intelligence, Xidian University,
Xi’an 710126, China (e-mail: <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>).
Digital Object Identifier 10.1109/TKDE.2025.3573899

to small, like a long tail [2], [3], [4]. The long-tailed data exists
in many real-world scenarios, such as medical image diagnosis,
face recognition, e-commerce, etc., commonly caused by the
intrinsic imbalance (naturally occurring frequencies of data) or
the extrinsic imbalance (collection or storage procedures).
In view of the outstanding performance of deep learning [5],
[6], [7], it has been widely applied in various applications and
achieved remarkable results [8], [9], [10], [11]. Whereas, the
deep model is generally designed based a class-balanced data
distribution, which causes that the model easily skews towards
the majority classes and meanwhile neglects the minority classes
for the long-tailed data [4], [12], [13], [14]. Besides, minority
classes may be more pivotal and attractive in some certain applications, such as anomaly detection [15], cancer detection [16],
network attack [17], etc. Therefore, it is significant to improve
the learning capability of the deep model on long-tailed data, so
that it can better learn the representation of minority classes and
mitigate the overfitting to majority classes.
At present, many methods [12], [13], [18], [19], [20], [21],
[22], [23], [24] have been proposed to improve the classification
performance of deep learning on long-tailed data, which can be
roughly categorized into class re-balancing, data augmentation,
and module improvement. Among them, data augmentation,
which adds additional knowledge to alleviate the information
scarcity of minority classes and enhance sample diversity, has
become one of the most effective approaches in deep long-tailed
learning. Early, SMOTE and some SMOTE-based methods [19],
[25] expanded the training data by interpolating between two
samples of the same class. Furthermore, mixup and its extended methods [20], [26], [27], [28] combined two arbitrary
sample-label pairs for augmentation, thereby improving the
model’s generalization ability. Subsequently, using Generative
Adversarial Networks (GANs) [29] to generate artificial training data has achieved good results in alleviating the scarcity
of minority classes information. Whereafter, FASA [30] was
proposed to address the data scarcity issue by generating new
features, especially for minority classes, based on a Gaussian
prior with their mean and variance estimated from real samples
in past iterations. It utilizes a loss-adapted manner to sample the generated virtual features to avoid overfitting. Later,
some methods [21], [31] were proposed based on knowledgetransfer (such as other datasets, tasks or classes). For instances,
Kim et al. [21] proposed M2m to translate samples from majority
classes to minority classes via optimizing an objective function

1041-4347 © 2025 IEEE. All rights reserved, including rights for text and data mining, and training of artificial intelligence and similar technologies.
Personal use is permitted, but republication/redistribution requires IEEE permission. See https://www.ieee.org/publications/rights/index.html for more information.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

4498

IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, VOL. 37, NO. 8, AUGUST 2025

with turbulence. Chu et al. [31] proposed OFA that used the
class activation maps to separate the features of each class into
class-generic and class-specific components and then combined
them to augment tail classes.
Noticeably, a default assumption is generally given in
the classification model: each category is independent and
discriminable with others, and most existing methods of data
augmentation are designed in accordance with this default assumption. Based on this assumption, it expects that the designed
classification model can try its best to achieve gathering together
samples from the same class and meanwhile separating samples
from different classes. Whereas, in some real-world applications, multiple categories are not always completely independent
with others, in contrast, they may be interrelated to others,
such as the fine-grained visual classification [32], [33], emotion
analysis [34], etc. The interrelation of different categories may
be the result of confusion or ambiguity among characteristics
of some samples, making it challenging to distinguish these
samples belonging to interrelated categories. Specifically, for
the long-tailed data, it is observed that some samples from
the minority classes are more likely to exhibit the confused
characteristics with some samples from other classes. This poses
a challenge in more accurately discriminating the minority
classes for the deep learning model. It brings a worth thinking
issue that whether some potential confused characteristics can be
utilized in data augmentation to alleviate this challenging or not.
However, most existing transfer-based data augmentation
methods are still defaulted that all classes are independent,
and they generate new samples for the target class based on
the information extracted only from arbitrary categories, without considering any interrelations among categories, such as
Remix [28], M2m [21]. This may cause that the selected samples
for generations are not critical so that difficultly generating more
representative new samples for the target class, which limits the
improvement of classification performance for long-tailed data.
Besides, when the interrelations among categories are ignored,
the utilized information for generation may include a substantial
amount of bias information and be deviated from the target class.
Fig. 1 shows an illustration of sample generations based
ignoring or considering the class-correlation,1 respectively. In
Fig. 1, the green triangle, orange diamond and blue circle represent samples from three categories (automobile, bird and truck,
respectively), where the automobile and bird are two majority
classes and the truck is a minority class. And the black solid line
is the initial classification boundary for three categories before
data augmentation. Based on the initial classification boundary,
it is seen that a sample (belonging to the truck, truck-C) is
incorrectly classified to the automobile, labeled in the circle with
blue-line. Here, the truck is regarded as the target class, and it is
expected that a sample from other classes (automobile or bird)
can be selected as the seed-sample to generate a new sample for
data augmentation.
1 The generation of ignoring the class-correlation acquires the information
by randomly selecting one class which may be unrelated to the target class. In
contrast, the generation of considering the class-correlation typically relies on
information from a class interrelated to the target class. Note that the interrelation
of multiple categories is shortened as class-correlation.

Fig. 1. An illustration of data augmentation with and without class-correlation,
where three categories (automobile, bird and truck) are from CIFAR-10-LT and
the sizes of their samples are successively decreases. By data augmentation
with (without) class-correlation, a new sample is generated to amend the classification boundary, labeled in the red (purple) dotted circle. The red (purple)
dotted line is the new boundary obtained by data augmentation with (without)
class-correlation. Obviously, the red new sample is more useful for amending
the classification boundary than the purple one, since the misclassified sample
is correctly classified by the red boundary.

Significantly, when all categories are considered independent
by default, a seed-sample is selected from all classes with some
probability in general. It may cause that an unrelated class (the
bird) is selected to take one bird image as the seed-sample
(labeled in the purple dotted diamond, bird-A), and then a new
sample (labeled in the purple dotted circle) is generated for
the target class (the truck) to amend the classification boundary, shown as the purple dotted line. Obviously, the amended
boundary is inadequate for correcting the prediction of that
misclassified sample, as shown light orange area in Fig. 1. In
contrast, when the class-correlation is considered and explored,2
a seed-sample (labeled in the red dotted triangle, automobile-B)
can be sought from the interrelated class (the automobile) based
on the explored class-correlation, to generate the new sample
(labeled in the red dotted circle). Distinctly, the initial classification boundary is amended based on that generated sample
into the red boundary, which can correct the prediction for the
misclassified sample, as shown light blue area in Fig. 1. This
illustrates that class-correlation may help generate more pivotal
and representative samples compared to its absence.
Furthermore, it is observed that some challenging (difficultto-classified) samples exhibit class-correlations, since there is
ambiguity feature between the challenging sample and other
samples, especially from the minority classes. In particular, the
challenging samples are not exclusively associated with one
single class, such as some samples which are easily incorrectly
classified (or adjacent to the classification boundary), since their
distribution in the feature space may be inevitably constrained by
several interrelated classes. As is well known, these challenging
samples may constitute a pivotal factor in promoting the capacity
of deep models to effectively learn from long-tailed data. It
implies that exploring the potential correlations among classes
is crucial for generating more representative samples in data
augmentation.
2 For CIFAR-10-LT, we explore the class-correlations among ten classes,
where the truck has higher relation to the automobile, and more details are
shown in the experiment of Section IV-D.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

QI et al.: DASCE: LONG-TAILED DATA AUGMENTATION BASED SPARSE CLASS-CORRELATION EXPLOITATION

Drawing upon the above analyses, we propose a new data
augmentation method based on the sparse class-correlation exploitation for deep long-tailed visual classification in this paper,
named as DASCE, which effectively utilizes the abundant information from related classes to generate more representative samples. In the proposed method, we first explore sparse potential
correlations among classes by employing orthogonal matching
pursuit, meanwhile utilizing the explored class-correlation to
generate pivotal seed-samples with rich correlation information.
Then, the generated seed-samples are translated to obtain more
representative new samples by the ambiguity-filtered targetsamples translation module. Furthermore, considering that the
discriminative feature is still obtained based on the assumption
of class-independence, the self-supervised feature is introduced
and integrated with the discriminative feature to exploring more
accurate class-correlations. In addition, a supplementary generation strategy is devised to remedy the issue of insufficient
challenging samples, since they are gradually decreased during
the training process. Finally, experimental results illustrate that
the proposed method obtains better performance with a handful
of generated samples than the comparison methods, which also
validates the representativeness of new samples generated by the
proposed method.
Compared with most existing methods, the contribution of the
proposed method mainly includes four items:
r We pose the class-correlation problem for the long-tailed
classification task, and propose a novel data augmentation
method based the sparse class-correlation exploitation,
which effectively generates more representative samples
for the target class to promote the performance only with
a small quantity of generated samples.
r We propose a sparse class-correlation exploration module to explore the potential correlations among classes.
Moreover, the self-supervised feature is introduced and
fused with the discriminative feature to explore the precise
class-correlations.
r Based on the class-correlation, samples related to the target
classes are used to construct more pivotal seed-samples.
Meanwhile, an ambiguity-filtered optimization objective is
designed to translate seed-samples into more representative
new ones, which advances the generated samples closer
to the target classes by enhancing class-consistency and
suppressing the class-deviation.
r Abundant experimental results validate the effectiveness
of the proposed method across various imbalance ratios,
especially under extreme imbalanced conditions. The additional experiments illustrate that the class-correlation is
considerable and effective for generating more representative samples for the long-tailed data.
The rest of this paper is organized as follows. Section II first
introduces the related works of long-tailed learning. Second,
Section III provides the detailed description of the proposed
method (DASCE). Then, experimental results are shown to
validate the effectiveness of DASCE in Section IV. Finally,
Section V provides the conclusion along with the prospects on
future works.

4499

II. RELATED WORKS
At present, a great quantity of studies have been developed
to address the long-tailed problem, mainly focusing on three
patterns: class re-balancing, module improvement and data augmentation. As follows, we will introduce some related methods
respectively from these three perspectives.
A. Class Re-Balancing
Class re-balancing [12], [13], [18], [30], [35], [36] is the
most common way for mitigating the problem of long-tailed
data, which seeks to re-balance the negative impact of imbalanced class sample sizes in the process of training. This pattern
is mainly achieved by designing reasonable weights for loss
function or sampling method, at sample-level or class-level,
including re-sampling and re-weighting strategies.
The instinctive practice of re-sampling are random oversampling (ROS) and random under-sampling (RUS). Generally,
ROS duplicates samples from the minority classes randomly,
and RUS deletes samples from the majority classes randomly.
Whereafter, some advanced sampling methods were proposed.
For examples, Wang et al. [35] proposed Dynamic Curriculum Learning (DCL) which continuously adjusted the sampling
probability of each class as the training progresses. Essentially,
the more samples are selected from one class, the lower the
probability it will be sampled later. Zang et al. [30] proposed
Feature Augmentation and Sampling Adaptation (FASA) to
expand the feature space especially for minority classes, to
address the issue of data scarcity. FASA also provided class-wise
sampling rates by exploring the model classification loss on a
balanced meta validation set, so as to favor minority classes in
the learning process.
As for re-weighting, a straightforward approach uses the
reciprocal of class sample size as the weight to adjust the
loss during training. Furthermore, Cui et al. [12] proposed
the Class-balanced loss (CB loss) which was independent of
label frequencies. The concept of effective number was introduced to measure the expected sample size per class, and it was
subsequently utilized for calculating weights in CB loss. Focal
Loss [18] was designed based on the observation that minority
classes exhibit greater prediction difficulty and lower prediction
probabilities compared to majority classes, and thus it weighted
samples inversely according to the prediction probability.
Wang et al. [36] initially examined the loss landscape in longtailed learning and developed a universal Balanced Gradient
Penalty (BGP) method, which guided the network more effectively and alleviated the sharpness without imposing an
additional computational burden. In short, although the class
re-balancing methods can improve the performance of minority
classes, they inevitably come with a trade-off for the majority
classes and fail to fundamentally address the issue of information
scarcity in long-tailed data.
B. Module Improvement
Module improvement aims to enhance the performance of
deep model in the context of long-tailed data by optimizing or

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

4500

IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, VOL. 37, NO. 8, AUGUST 2025

designing its structure. Many methods [22], [23], [24], [37] have
been proposed, primarily from the perspective of representationlearning and classifier-designing. For examples, Ouyang et al.
introduced Hierarchical Feature Learning (HFL) [22], which
drew inspiration from the perspective that each class possesses
individuality in discriminative visual representation. HFL organized objects into visually similar class groups in a hierarchical
manner, constructing a cluster tree to learn representations for
these groups separately. This hierarchical structure facilitated
the gradual transfer of knowledge from groups with massive
classes to their sub-groups with fewer classes. Kang et al. [23]
introduced a k-positive contrastive learning method, which effectively combined the strengths of supervised and unsupervised
learning to acquire a more balanced feature space and enhance
the generalization of the model. Tang et al. utilized a causal
classifier [37]. It implements causal intervention during training
and counterfactual reasoning during inference, to eliminate the
bad (misleads the tail prediction biased towards the head) while
keeping the good (benefits the representation learning and head
prediction).
Besides, ensemble-based approaches are currently widely
employed and have demonstrated commendable performance
by combining predictions from diverse models. For instance,
Zhou et al. [24] proposed a unified Bilateral-Branch Network
(BBN), which incorporated two branches, to concurrently handle representation learning and classifier learning, effectively
alleviating the long-tailed problem. In BBN, one branch is the
conventional learning branch and the other is the re-balancing
branch. Although the module improvement can achieve satisfactory performance, it is likely to face greater design difficulties
and higher computation complexity.
C. Data Augmentation
Data Augmentation focuses on introducing extra knowledge
into model training, to expand the original dataset and enhance the performance. Early, some conventional augmentation
methods [19], [20], [21], [25], [26], [27], [28], [31], [38] were
proposed to enhance the size and quality of the training data.
SMOTE [19] and its extended methods [25] generated samples
by interpolating between samples from the same class. Furthermore, mixup-based methods [20], [26], [27], [28] generated a
new sample-label pair through the linear combination of two
samples from arbitrary classes, so as to acquire performance
gains for minority classes.
In subsequent developments, transfer-based augmentation
methods have been employed to transfer knowledge from a
specific source domain to the target domain. These methods
involve utilizing information from majority classes to enhance
the representation of minority classes. Chu et al. [31] decomposed features into class-generic and class-specific components
by class activation maps. Based on this, minority classes were
augmented according to the new samples obtained by fusing
majority class-generic and minority class-specific features together. Instead of the decomposition feature, Kim et al. [21]
proposed M2m to translate majority class samples to minority
class ones by optimizing a function with an added disturbance.

Furthermore, model pre-training also serves as a transfer-based
augmentation method. For instance, Domain-Specific Transfer
Learning (DSTL) [38] undergoes pre-training on long-tailed
datasets to facilitate representation learning, followed by finetuning on more balanced data. This process can emphasize the
acquired features towards minority classes and achieve a more
balanced performance across different classes.
In short, data augmentation methods can significantly enhance
the model’s ability to distinguish minority classes by introducing additional information specific to these classes, while
reducing the impact on the performance of majority classes.
However, in practice, most existing methods generate new samples by randomly selecting seed-samples, from the perspective
of the class-independence. When the information utilized by a
data augmentation method is randomly acquired from arbitrary
classes, it results in a deficiency of representative data during the
generation process, potentially introducing bias information that
adversely affects model training. Compared with most existing
methods, we explore and utilize the correlations among classes
to augment training data, enabling more representative sample
generation.
III. THE PROPOSED METHOD
In this paper, we propose a new long-tailed data augmentation method based sparse class-correlation exploitation, named
DASCE. Different from most existing methods defaulting an
assumption that each class is independent with others in the
general classification model, the proposed method explores the
sparse class-correlation among different classes at the first time,
and utilizes the class-correlation to generate more representative samples for augmenting long-tailed data. Fig. 2 shows the
framework of DASCE, mainly including four parts: CorrelationInformed Seed-Samples Generation, Sparse Class-Correlation
Exploration, Ambiguity-Filtered Target-Samples Translation,
and Model Training. As follows, we will introduce the proposed
method in details.
A. Correlation-Informed Seed-Samples Generation
In the data augmentation process, seed-samples are typically
selected as the key information source and then modified or
expanded to generate additional samples, aiming to balance
the class distribution in long-tailed datasets. Particularly, the
seed-samples are more pivotal, the generated new samples
are more representative, which is beneficial for improving the
model’s classification performance. Most existing methods of
data augmentation employ random-selection strategies to obtain
seed-samples from arbitrary classes under the assumption of
class-independence. However, this assumption may cause that
some methods generate unrepresentative samples, which could
be less effective in mitigating the long-tailed problem. Hence,
we first design a correlation-informed seed-samples generation
module to generate more pivotal seed-samples by leveraging the
information from some classes interrelated to the target class.
Considering that challenging samples may involve ambiguous
and intricate boundaries, they are often difficult to classify
correctly and exhibit some interrelations with other samples.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

QI et al.: DASCE: LONG-TAILED DATA AUGMENTATION BASED SPARSE CLASS-CORRELATION EXPLOITATION

4501

Fig. 2. The framework of the proposed method (DASCE), which shows the process of generating a new sample x∗o for the target class c and incorporating this
sample into the training set to balance the class-distribution.

It suggests that these challenging samples may be significant for
enhancing the model’s classification capability on long-tailed
data, especially for minority classes where most challenging
samples originate. Based on this, seed-samples are generated
from challenging samples, which are defined as misclassified
samples in the proposed method. As follows, we will take the
generation of one seed-sample (xs ) as an example to introduce
the detail of Correlation-Informed Seed-Samples Generation.
Given a training set S with the long-tailed distribution, containing N samples, S = {(xn , yn )}N
n=1 , where yn represents
the given label of the sample xn . Here, yn ∈ {1, . . ., C}, and
C is the size of classes. Besides, we use Ni as the number of
samples belonging to the ith class,
 meanwhile assuming that
N1 ≥ N2 ≥ . . . ≥ NC , where C
i=1 Ni = N . In the proposed
method, there are two main networks: a network g for generating
new samples and a network f for the classification, where g has
been trained on the raw training set and f will be iteratively
trained on the augmented training set.
1) Determining the Target Class for Generation: Before generating the seed-sample, the generative target class (c) must be
specified. We first design a strategy to determine c. When a
sample xo (belonging to the ith class (ci )) is misclassified into
the jth class (cj ), the target class c is defined as
c = arg min Nc ,
c ∈{ci ,cj }

(1)

where Nc denotes the number of samples in the class c . Note
that the target class c corresponds to the class with fewer samples.
This strategy automatically adapts the quantity of generated
seed-samples for each class, with a particular emphasis on
supporting minority classes.
2) Generating the Seed-Sample Based on Sparse ClassCorrelation: After determining the target class c, the misclassified sample xo is used to generate a more pivotal seed-sample
xs for this class. In this process, the balanced dictionary D
is acquired by sampling uniformly from all classes except the
class c. Based on the dictionary D, the correlation ω among
the sample xo and these selected samples from the dictionary
is obtained by the spare class-correlation exploration module.
Then, the seed-sample xs is generated by
xs = Dω.

(2)

For the class-correlation exploration, more details will be introduced in Section III-B.
3) Supplementary Generation Strategy: As the network converges, the count of misclassified samples gradually decreases
and eventually stabilizes at a low level, which leads to a reduced number of generated samples. Therefore, we design a
supplementary generation strategy that leverages the accuracy of
the balanced validation set to guide the generation of additional
seed-samples. In detail, when there are no misclassified samples
for a certain class (ci ) in a mini-batch, and meanwhile the
accuracy (accci ) is less than 1 on the validation set, the number
of samples that still need further generation for the class ci is
determined by the following equation:
ci
Nsup
= (1 − accci ) × Nbatchci,

(3)

where · donates the floor function, and Nbatchci is the size of
samples belonging to the class ci from the corresponding minici
samples from
batch. Following that, we randomly selected Nsup
the class ci for supplementary generation, in a manner similar
to the use of misclassified samples, with ci being designated as
the target class. Similar to the generation based on misclassified
samples, this generation process still includes creating a seedsample followed by its subsequent translation.
B. Sparse Class-Correlation Exploration
In the task of image classification, the correlations among
classes are generally existed and non-ignorable. Specifically, this
class-correlation is typically sparse in real-world applications,
since one class can not be related to most classes. Based on this,
we propose a sparse class-correlation exploration module using
orthogonal matching pursuit (OMP) [39] to obtain accurate
class-correlation characterized by sparsity for the generation
of seed-samples. In this module, we first extract deep fusion
features and then explore the class-correlations by mining the
relationships between samples of different classes.
1) Depth Fusion Features: Compared with the pixel-level
features, the high-dimensional features extracted by deep learning models are more expressive, robust and interpretable.
Whereas, the supervised discriminative features possess strong
discrimination, which are inadequate for analyzing the classcorrelations, since they are obtained based on the default
assumption where all classes are independent. Therefore, we

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

4502

IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, VOL. 37, NO. 8, AUGUST 2025

introduce the self-supervised model to capture the inherent
features, which is unrestricted by the assumption of classindependence. A classical self-supervised model (SimCLR [40])
is used to obtain the inherent feature for each sample, and the
network g is used to extract the discriminative feature. The fusion
feature is obtained by concatenating the disciminative feature
with the latent inherent feature to explore the class-correlations,
and the fusion feature of sample xn is denoted as xfn .
2) Exploring Sparse Class-Correlations Via OMP: In fact,
the class-correlations are mainly reflected in the similarities
among some samples from each class, especially among these
ambiguous samples, shown as Fig. 1. Based on this, the proposed
module explores the potential class-correlation from the view of
samples, by using OMP to mine the explicit correlations among
the misclassified sample and samples from non-target classes
based the fusion feature.
Given a misclassified sample (xfo ) and the target class c, we
first construct a dictionary matrix Df by uniform sampling from
all classes (except the target class), Df = (xfd1 , xfd2 , . . ., xfdM ),
where each sample is equivalent to an atom of the dictionary, M
is the number of atoms. Based on OMP, an optimal combination
is sought from Df to maximally approximate the sample xfo ,
and the optimization function is formulated as
ω = arg min ||xfo − Df ω||22 + γ||ω||0 ,
ω

(4)

where ω expresses the weight vector of the optimal combination,
xfo is the feature vector of xo , and the 0 norm is used to constrain
the sparsity of ω.
In general, this optimization is achieved by minimizing the
residual (r) between xfo and its corresponding combination
iteratively, shown as
r = xfo − Df ω.

(5)

In the optimization process, the misclassified sample xfo is first
set as the initial residual r0 . At the kth iteration, an atom (xfmk )
is sought from the dictionary Df by minimizing the kth residual
(rk ), which is satisfied as
mk = arg max | < rk−1 , xfm > |,

(6)

m

where mk is the index of the sought atom in Df , mk ∈
{d1 , d2 , . . ., dM }, and M is the number of atoms in Df . Then,
the atom xfmk is added to update the candidate set Φ[k] of the
optimal combination,
Φ[k] = [Φ[k − 1], xfmk ].

(8)

Based on Φ[k] and ω k , the residual is calculated by
rk = xfo − Φ[k]ω k ,

(9)

where k = {1, 2, . . ., K}. Finally, an optimal combination is
obtained by K iterations to best approximate xfo , shown as
K f
xfo ≈ ω1K xfm1 + ω2K xfm2 + · · · + ωK
xmK ,

xfo ≈ Df ω,

(10)

(11)

where the weights of selected atoms are their corresponding
values in ω K , and the weights of unselected atoms are given
zero value. Based on the obtained sparse weight vector ω, a
pivotal seed-sample xs can be generated by (2).
The class-correlation exploration module explores the correlation between the misclassified sample and non-target class
samples, which essentially represents class-correlation as the
calculation is between one class and multiple classes. This process enhances the representativeness of the information used in
generation. The OMP algorithm ensures that the atoms selected
at each iteration are as orthogonal as possible to the previously
selected atoms, leading to significant differences between atoms.
In fact, each atom with a large difference corresponds to a
specific characteristic in the misclassified sample. Therefore,
our method can effectively capture a sparse representation of
the various characteristics of misclassified samples. The differences between atoms drive the process to explore diverse
related classes, thereby increasing the diversity of the acquired
information.
C. Ambiguity-Filtered Target-Samples Translation
By the correlation-informed seed-samples generation module, we have generated more pivotal seed-samples, whereas they
may not be adequate for direct use in classification. Some correlated samples, whose corresponding weights in ω are non-zero,
may inevitably contain redundant information or noise. This may
misguide the learning process of the deep model. Moreover, the
ambiguous information from related samples may negatively
impact the classification of interrelated classes, so it needs to
be filtered, retaining only the key and useful information. We
must further align the seed-samples with the target class and
ensure the high class-consistency and low deviation from the
target class in generation possibly.
Based on this, an ambiguity-filtered target-samples translation module is designed to achieve the translation from the
seed-sample into the new sample of the target class. In this
module, an optimization problem for generating the new sample
is constructed as

(7)

Meanwhile, the weight vector ω k of the selected atoms
[xfm1 , xfm2 , . . ., xfmk ] is calculated by
ω k = (Φ[k]T Φ[k])−1 Φ[k]T xfo .

K
]. Note that only a few atoms
where ω K = [ω1K , ω2K , . . ., ωK
are selected in the optimization process, and K
M . Thus, a
sparse weight vector ω is obtained to achieve the maximization
approximation

x∗o = arg min L(g; xs , c) + λ
xs :=xs +δ

C


Wv · fv (xs ),

(12)

v=1

where c is the target class, and δ represents small noises. In
(12), the first term (L) calculates the loss of the sample xs
with the ground truth c given by the network g, to force the
seed-sample more closely with the target class for the classifier,
and the cross-entropy loss is used. The second term is a designed
regularization term with λ as the coefficient, where fv (xs )
represents the prediction of classifying the sample xs to the vth
class by the network f . And Wv expresses the relational degree
of the vth class to the target class for the seed-sample xs , and it

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

QI et al.: DASCE: LONG-TAILED DATA AUGMENTATION BASED SPARSE CLASS-CORRELATION EXPLOITATION

is calculated based the explored correlation by

|ωi |
i ∼v
Wv = ωM
,
i=1 |ωi |

Algorithm 1: The Pseudo Code of DASCE.
(13)

where ‘∼’ expresses that the selected sample (corresponding
to the weight ωi ) belongs to the vth class, and v = {1, . . ., C}.
Here, the gradient descent method is employed to optimize (12),
achieving the translation from a seed-sample to a new targetclass one, and this process is described by


C

Wv · fv (xs ) ,
(14)
θ ← xs L(g; xs , c) + λ
v=1

and
xs ← xs − α · θ,

4503

(15)

where the parameter α represents the optimization step size.
By optimizing (12), the cross-entropy loss term L brings the
seed-sample closer to the target class, while the regularization
term pushes the seed-sample further away from its original (nontargets) classes. Finally, a synthetic sample x∗o belonging to the
target class (c) is obtained based the seed-sample xs .
D. Training the Classification Model
The proposed method (DASCE) is an end-to-end deep network learning model with integrated data augmentation. It enhances each mini-batch during training by incorporating newly
generated samples into the original mini-batch. The pseudo
code of DASCE is illustrated in Algorithm 1, and it indicates
that the proposed method generates data for each mini-batch
during training, which enables generation to be dynamic and
org
expresses the set of original
data-adaptive. In Algorithm 1, Sm
samples selected from one mini-batch Sm based on the misclassification or the supplementary generation, and SCCE denotes
the sparse class-correlation exploration module.
IV. EXPERIMENTS AND ANALYSES
In this section, we will validate the effectiveness of the proposed method from five items: the classification performance
comparison of the proposed method with existing methods, the
analyses on the quality of generated samples, the analyses on
the explored class-correlations, the analyses on the fusion of
features, and the analyses on different imbalanced ratios. All
experimental results and analyses will be detailedly shown in
the following parts.
A. Experimental Datasets and Settings
In this part, we will first provide the details about experimental datasets, comparison methods, evaluation metrics, and
experimental settings.
1) Experimental Datasets: We conducted the experiments
on two benchmark datasets: CIFAR-10 and CIFAR-100. Both
CIFAR-10 and CIFAR-100, collected by Alex krizhevsky
et al. [41], are real-world image recognition datasets, including
a variety of animals, plants, vehicles etc., with large noises
and varying object proportions, where all images are 32 × 32

Require: A long-tailed dataset S = {(xn , yn )}N
n=1 ; the
parameters T , λ, α.
Ensure: The augmented sample set Saug , and the
updated training model f .
1: for batch Sm ⊆ S do
org
from Sm
2: Obtain Sm
3: Snew ← {}
org
do
4: for xo in Sm
5:
c ← the target class calculated by (1)
6:
D ← the balanced dictionary from S
7:
xfo , Df ← deep fusion features of xo , D
8:
ω ← SCCE(xfo , Df )
9:
xs ← D ·ω
10:
for t = 1 to T do

11:
θ ← xs [L(g; xs , c) + λ C
v=1 Wv · fv (xs )]
12:
xs ← xs − α · θ
13:
end for
14:
Snew ← Snew ∪ {(x∗o , c)}
15: end for
16: Saug ← Sm ∪ Snew
17: Train f with Saug
18: end for

three-channel color images. For CIFAR-10, there are 10 classes,
and each class contains 5000 training and 1000 testing images.
For CIFAR-100, it has 100 classes, and each class contains 500
training and 100 testing images.
Based on two datasets, we artificially construct their longtailed versions by randomly sampling [21], named as CIFAR-10LT and CIFAR-100-LT. Considering that the sample size follows
an exponential decay across different classes of long-tailed data,
we calculate the sample quantities of each class in CIFAR-10-LT
and CIFAR-100-LT over various imbalance ratios by using an
exponential function, demonstrated as
c−1
  C−1
1
,
(16)
Nc = Nmax ·
ρ
where Nc is the number of samples belonging to the class c
in the long-tailed training set, C is the number of classes, c ∈
{1, . . ., C}. Nmax is the number of samples belonging to the
maximum class, and ρ expresses the imbalance-ratio. Without
loss of generality, the class with a larger index (c) is considered
to have a fewer samples. And Nc satisfies the following formula

Ni ≥ Nj , ∀i, j, 1 ≤ i < j ≤ C
.
(17)
Nmax = N1
By (16), the sample size for each class is calculated, and
then the long-tailed versions of CIFAR datasets with various
imbalance ratios (ρ) are obtained by random sampling. Table I
shows the distributions of the training data for CIFAR-10-LT and
CIFAR-100-LT. Furthermore, the testing and validation sets are
class-balanced.
2) Comparison Methods: In experiments, fourteen methods
are used for comparisons, shown as follows:

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

4504

IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, VOL. 37, NO. 8, AUGUST 2025

TABLE I
THE CLASS DISTRIBUTIONS OF TRAINING DATA FOR CIFAR-10-LT AND CIFAR-100-LT UNDER DIFFERENT IMBALANCE RATIOS

r Deferred Re-sampling (DRS) [13]: it uses the deferred re-

r CR-CE [47]: CR introduces a curvature regularization to

sampling strategy to obtain the balanced mini-batches from
the training set and send them to the network in the later
stage of training.
r SMOTE [19]: it is a classical sample expansion method of
interpolation between similar samples.
r Focal Loss [18]: it weakens the learning of easy samples
and strengthens the learning of challenging samples by
introducing a modulating factor into the standard crossentropy loss, thereby enhancing the classification ability.
r Mixup [20]: it adds two sample-label pairs proportionally
to generate a new sample-label data.
r L2Rw [42]: it minimizes the loss on an unbiased validation
set by executing a meta gradient descent step on the current
mini-batch sample weights, thereby weighting the training
samples based on their gradient directions.
r CB Loss [12]: it re-weights the loss based on the inverse
ratio of effective numbers.
r Meta-Weight-Net [43]: it adaptively learns an explicit
weighting function (an MLP with one hidden layer) for
re-weighting, guided by a balanced validation set.
r LDAM-DRW [13]: it constructs the loss by minimizing
a margin-based generalization bound, and uses the delay
re-weighting strategy to learn the initial feature first.
r ReMix [28]: it is a generation method based on Mixup,
which linearly combines the features and labels with different mixing factors, has characteristics that it focuses
more on minority classes when giving new sample labels.
r RCBM-CE [44]: it employs a meta-learning approach
to explicitly estimate the differences between the classconditioned distributions, thereby enhancing the traditional
class-balanced learning.
r M2m [21]: it transforms majority class samples into minority ones to construct a more balanced training set via
optimizing a designed objective function, which is a typical
transfer-based augmentation;
r IB Loss [45]: Influence Balanced (IB) loss measures the
extent to which each sample affects the decision boundary,
allowing weights to be adaptively allocated based on the
influence of samples on the decision boundary.
r IDRS [46]: it introduces the concept of sample learning speed to measure its difficulty, dynamically adjusting the sampling probability for re-sampling. Note that
IDRS is the abbreviation named by us for convenient
exhibition.

help the model learn curvature-balanced and smoother
perceptual manifolds, inspired by the idea that curvature
imbalance may also lead to model bias.
In fourteen compared methods, DRS, Focal Loss, L2Rw,
CB Loss, Meta-Weight-Net, LDAM-DRW, IB Loss, IDRS and
CR-CE are all class re-balancing methods, where DRS and IDRS
achieve the re-balancing by the re-sampling strategy, CR-CE is
unique which uses regularization within perceptual manifolds,
and the rest achieve the re-balancing by re-weighting. Besides,
SMOTE, Mixup, ReMix, RCBM-CE and M2m belong to data
augmentation methods. Additionally, the backbone network
(ResNet-32 [6], [48]) without adding any long-tailed learning
strategies is used as a baseline for comparison, which is trained
with the cross-entropy loss.
3) Evaluation Metrics: The classification accuracy serves as
the primary evaluation metric in the experiment, to analyze the
performance of DASCE across all and partial classes. Referred
to [21], all classes of each data are divided into three parts in
experiments: the majority classes, the middle classes and the
minority classes. Concretely, the accuracies of the majority,
middle and minority classes are respectively calculated by
min −1
maj
c=maj+1 Pc
c=1 Pc
, Amid = min −1
,
Amaj = maj
c=1 Nc
c=maj+1 Nc
C
Pc
and Amin = Cc=min ,
c=min Nc
where maj = C/3, min = C − maj + 1, C is the number of
classes. Pc represents the number of samples correctly classified
in the cth class. Specifically, for CIFAR-10 (CIFAR-100), its
majority classes are from 1-st to 3rd (from 1-st to 33rd) classes,
its middle classes are from 4th to 7th (from 34th to 67th) classes,
and its minority classes are from 8th to 10th (from 68th to 100th)
classes.
4) Experimental Settings: In experiments, all methods are
trained on the long-tailed data and verified on the balanced validation and testing data. The proposed method employs ResNet32 [6], [48] as the backbone network, encompassing the main
network f , the self-supervised network (SimCLR [40]), and
the generation network g (trained without using any long-tailed
learning method). For parameters, the learning rate of the network is set as {0.1, 0.3, 0.4}, and the decay rate is selected from
{2e−4 , 4e−4 }. For the regularization term coefficient λ, it is set as

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

QI et al.: DASCE: LONG-TAILED DATA AUGMENTATION BASED SPARSE CLASS-CORRELATION EXPLOITATION

4505

TABLE II
TOP-1 ACCURACY ON CIFAR-10-LT AND CIFAR-100-LT UNDER DIFFERENT IMBALANCE RATIOS, WHERE THE BEST RESULTS ARE HIGHLIGHTED IN BOLD AND
THE SECOND BEST RESULTS ARE UNDERLINED FOR CLARITY

{0.001, 0.01, 0.7}. Moreover, our model uses a delay strategy,
starting the generation from the 161-st epoch and ending the
training at 200th epoch. And the iteration number K of OMP is
set to 70, 7 or 5.

B. Comparisons of Classification Performances
In this part, we compare the classification performance of
DASCE on two datasets with 14 existing methods and the
backbone network trained without any long-tailed learning
method, under three imbalance ratios (ρ =10, 100 and 200).
The experimental results are shown in Table II, where the best
result is highlighted in bold and the second best is underlined
for clarity. Moreover, considering that the fusion feature is
used to explore the class-correlation in our method (DASCE),
we also analyze the performance of DASCE only with the
discriminative feature and the self-supervised inherent feature
respectively. In tables, ‘DASCE-SF’ expresses the proposed
model only with the discriminative feature, ‘DASCE-SSF’
expresses the proposed model only with the self-supervised
inherent feature, and ‘DASCE-Fusion’ expresses the proposed
model with the fusion feature.
From Table II, it is seen that the proposed method is superior
to most of comparison methods, especially for ρ = 100 and
ρ = 200. For CIFAR-10-LT and CIFAR-100-LT, while ρ = 100
and ρ = 200, the best performances are obtained by DASCEFusion, meanwhile the second best performances are obtained
by DASCE-SF or DASCE-SSF. It demonstrates that DASCE
performs well under high imbalance ratios and is conducive
to enhance the deep model’s effectiveness on the long-tailed
data. Meanwhile, it is also observed that DASCE-Fusion with
feature fusion is better than DASCE-SF and DASCE-SSF, which
validates that the fusion feature is benefit for exploring a more
accurate and reliable class-correlation. More analyses on the
feature fusion are given in Section IV-E.
Moreover, we also observe that the performance advantage
of DASCE is more pronounced on CIFAR-100-LT than on

Fig. 3. The Accuracy Comparison of M2m and DASCE in a training process
starting from the 155th epoch.

CIFAR-10-LT. This observation implies that fine-grained classification challenges tend to exhibit stronger inter-class correlations, which further underscores the efficacy of our method in
enhancing model learning. More analysis about class-correlation
exploration will be given in Section IV-D.
Notably, the higher the imbalance ratio is, the comparatively
fewer samples the minority classes contain. To better evaluate the
performance of DASCE for the minority classes and understand
why it performs well under high imbalance ratios, we also
analyze its performance on partial classes (majority, middle,
and minority). The experimental results on CIFAR-10-LT are
shown in Table III, where Amaj , Amid and Amin represent
accuracies of the majority classes, the middle classes, and the
minority classes, respectively, and ACC expresses the overall
accuracy.
From Table III, it is obvious that the proposed method
(DASCE-Fusion) improves the performance on the minority
classes by more than 6% on ρ = 100 and ρ = 200, compared
with other methods. It demonstrates that DASCE effectively
improves the classification performance for long-tailed data
by enhancing the learning capability of the deep model for
minority classes. Additionally, although the performance on the
majority classes is slightly lower than the compared methods
(such as DRS, Focal Loss, CB Loss, M2m and backbone),

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

4506

IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, VOL. 37, NO. 8, AUGUST 2025

TABLE III
TOP-1 ACCURACY OF MAJORITY, MIDDLE AND MINORITY CLASSES ON CIFAR-10-LT

Fig. 4. Number of Generated Samples Comparison of M2m and DASCE in a
training process after the 160th epoch.

the proposed method still obtains the best overall performance
(ACC) among all methods. According to our analyses, DASCE
seems to utilize information from majority classes more heavily
for generating minority classes, possibly overlooking the
generation for the majority classes. The outstanding overall
performance demonstrates that our method achieves a superior
trade-off when learning across various classes, even though it
may slightly compromise the performance of majority classes
compared to other methods. In short, the comparison results
demonstrate that the proposed class-correlation exploration is
helpful to generate more representative samples of the minority
classes, since it can utilize the richer relevant information from
multiple classes without the assumption of class-independence.
And the diversity among information used for generating new
samples can mitigate the over-fitting, especially when the
number of categories is larger.
C. Analyses on Quality of Generated Samples
In order to better validate the quality of new samples generated
by the proposed method, we conduct an analysis. Generally
speaking, if the performance can be improved better by a small
quantity of generated samples than a large quantity of generated
samples, it implies that the slight generated samples may be
of higher representativeness and qualities than the numerous
generated samples. Therefore, we observe the classification performance and the number of generated samples during training
on CIFAR-10-LT with ρ = 100. We compare DASCE with a
typical transfer-based data augmentation method (M2m) [21]
which randomly selects seed-samples without considering the
class-correlation. Fig. 3 shows the accuracies in some epochs
during the training process, and Fig. 4 exhibits the sizes of

generated new samples, where the results of DASCE and M2m
are labeled respectively in red and blue. Note that the generation
is implemented starting from the 161-st epoch.
From Figs. 3 and 4, it is seen that DASCE achieves superior
accuracies with fewer generated samples per iteration compared
to M2m. Obviously, in the first generation (the 161-st epoch),
DASCE achieves a performance improvement of 11% (from
63.2% to 74.1%) by adding 2627 new generated samples, when
M2m only increases the improvement of 3% (from 66.2% to
69.6%) with 5328 generated samples. It indicates the samples
generated by DASCE are more representative than M2m. Meanwhile, DASCE has reached more than 78% accuracy after 10 iterations with generation, with the faster convergence, and it finally
obtains the 2% improvement with considerably fewer generated
samples. These results indicate that the samples generated by
DASCE better represent the target classes distributions, thereby
enhancing classification performance, especially on long-tailed
datasets.
In addition, we conduct a comprehensive analysis on the
distribution from the original data to the generated data, utilizing
t-SNE [49] for dimensionality reduction and visualization. Fig.
5 depicts the specific data distribution at the 165th epoch (that
is of stable and rapid generation) corresponding to the 77.2%
accuracy. In Fig. 5, the sub-figure (a) represents the overall
distribution of the training set, where the local area Area-A
(labeled in the black dashed box) serves as a specific zone for
close-up visual observations of distributions both before and
after sample generation. (b) and (c) are the distributions of
Area-A before and after data augmentation with classification
situation, respectively. Note that the samples within the blue
dotted box are correctly classified due to data augmentation. (d)
and (e) show the sample distribution with generation based on
misclassified samples and supplementary strategy, respectively.
Asterisks represent generated samples based on misclassified
samples, while plus signs signify new samples generated via the
supplementary strategy.
From Fig. 5(a), it is easily seen that there are overlaps among
the sample distributions of different classes. This indicates
the presence of ambiguity and indivisibility among classes,
which may be an embodiment of class-correlation. Obviously,
it is difficult to correctly classify the samples in the overlapping area (especially for minority classes), and enhancing
the classification ability for them are certainly crucial for the
deep long-tailed learning. Comparing Fig. 5(b) with (c), it is
evident that the samples within the blue dashed boxes no longer

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

QI et al.: DASCE: LONG-TAILED DATA AUGMENTATION BASED SPARSE CLASS-CORRELATION EXPLOITATION

4507

Fig. 5. An illustration of the sample distribution at 165th epoch. In (a), (b) and (c), the hollow circles represent misclassified samples. In (d), the asterisks denote
the generated samples based misclassified samples, and the plus signs signify new samples generated via the supplementary strategy in (e).

encounter misclassifications after training with augmented data,
which indicates an improvement in the ability of the network to
discriminate this class achieved by our augmentation. From Fig.
5(d) and (e), it is seen that the samples generated by DASCE
fill the original sample distribution range on the one hand, and
reasonably extend the boundary of the minority classes on the
other hand. In short, the visual results demonstrate that DASCE
is able to generate more representative and diverse samples, both
within the core of existing categories and along their boundaries.
The generated samples can help balance the class-distribution
and contribute to an improved classification capability for the
respective categories within the model.
D. Analyses on Class-Correlation Exploration
In the proposed method, the class-correlation is crucial to
generate more representative samples, since it is greatly benefit
for generating samples with target class characteristics. Thus, in
this part, we conduct an analysis of class-correlation exploration
to validate its effectiveness.
In this experiment, we observe the class-correlations explored by DASCE at the initial epoch (epoch=161) and the last
epoch (epoch=200), where the experiment is implemented on
CIFAR-10-LT with the imbalanced ratio ρ = 100. For visualization, the correlation matrices are constructed to illustrate the
class-correlations explored by DASCE, in which each element
expresses the degree of correlation between the categories corresponding to its row and column. Specifically, in one epoch,
we accumulate and normalize all class-correlation vectors W =
[W1 , W2 , . . ., WC ] (as calculated by (13)) computed based on
target class c (c = 1, 2, . . ., C) as a row of the correlation
matrix, which represents the degree of interrelation between
the class c and other classes. Meanwhile, two misclassification
matrices3 are also provided alongside one correlation matrix
to illustrate the misclassification results (situations) before and
3 The misclassification matrix is computed by subtracting the number of
correct classifications from the confusion matrix and normalizing each row, in

after generation based on the corresponding correlation. The
experimental results are shown in Fig. 6. Note that “airplane”
and “automobile” are respectively abbreviated as “plane” and
“auto” for brevity.
From Fig. 6(b) and (e), it is seen that DASCE has indeed
discovered a rich and diverse range of class-correlations. For
example, our model can explore the strong correlations between truck and automobile (0.37 in the 161-st epoch and
0.69 in the 200th epoch), as well as between dog and cat.
Obviously, this discovery aligns with human perception, and
it validates the existence of class-correlation and our method’s
effectiveness in exploring it. Additionally, it is observed that
the misclassified matrices are similar to the correlation matrix,
that is, the distribution of parts with large values (deep color) is
similar. This implies that misclassified situation can be regarded
as a form of rough class-correlation. But, more detailed and
comprehensive relevance information can be explored by our
method instead of misclassified situation. From Fig. 6(a), (c)
and (f), the misclassification matrices are difficult to reflect
the implicit relationship between truck and ship. Therefore, the
class-correlations explored by DASCE are more accurate and
comprehensive, taking into account all categories.
Furthermore, by comparing the classification accuracies depicted in (a) and (c) of Fig. 6, it becomes evident that the
generation of DASCE significantly enhances the learning of
deep models for long-tailed data. Particularly, for minority
classes, such as the ‘truck’ class with the smallest number of
samples in the training set, accuracy has surged from 0.00 to
0.88. From Fig. 6(e), it is observed that at the 200th epoch (the
end of the training), the color of correlation matrix appears to be
relatively uniform, which indicates that the correlations among
multiples classes are ambiguous at this time. Because few of
misclassified samples remains during the training of the last
epoch, and most of them are characterized by noisy values and
which each element represents the ratio of samples misclassified from the row
category into the column category.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

4508

IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, VOL. 37, NO. 8, AUGUST 2025

Fig. 6. Misclassification Matrices and Correlation Matrices. (a) The Misclassification Matrix and accuracy on the training set of 160th epoch. (b) The Correlation
Matrix of 161-st epoch. (c) The Misclassification Matrix and accuracy on the training set of 161-st epoch. (d) The Misclassification Matrix and accuracy on the
training set of 199th epoch. (e) The Correlation Matrix of 200th epoch. (f) The Misclassification Matrix and accuracy on the training set of 200th epoch. Darker
colors indicate larger values, signifying stronger relevance.

distinct anomalies. These samples should be handled during data
preprocessing, and their detailed study is not within the scope
of this investigation. Besides, in the later stages of training, the
model has shown signs of convergence and has successfully
learned the minority classes, resulting in no further improvement
in training set accuracy.
E. Analyses on the Fusion of Features
Since the discriminative feature is obtained based on supervised information of long-tailed data distribution, it may be not
sufficient for exploring more accurate class-correlations. Therefore, we introduce the self-supervised learning model to extract
the latent inherent feature from long-tailed data, and then fuse it
with the discriminative feature to explore the class-correlation.
In the section IV-B, the experimental results in Tables II and III
have validated that the model with the fusion feature (DASCEFusion) is more effective than only with the discriminative feature (DASCE-SF) and the latent inherent feature (DASCE-SSF).
In this part, to better visualize the explored class-correlation
based on different features, we track the process of generating
samples with exploring the class-correlations based on three
features (fusion, discriminative and inherent features).
In this experiment, we randomly select a misclassified sample
at the 161-st epoch to observe the generation process based
three different features. Fig. 7 shows the generation processes
corresponding to three features (DASCE-SF, DASCE-SSF and
DASCE-Fusion) from the top to the bottom. In Fig. 7, from left
to right, the first image is a misclassified sample that is from the
class-9 and is predicted into the class-1, and then the class-9 is

as the target class. The next five images per row (located in the
gray box) express the atom-images that are sought by our sparse
class-correlation exploration and used to generate seed-samples,
and their corresponding weights and the true labels are annotated
respectively above and below each image. Next, the images are
the generated seed images based on the five atom-images, and
the last images are the final generated new samples.
From Fig. 7, it is seen that the new samples generated
by DASCE-Fusion and DASCE-SSF are more similar to the
misclassified sample than the one generated by DASCE-SF,
visually such as the target shape, color, background and other
aspects. By observing the five atom-images, it is found that
both DASCE-Fusion and DASCE-SSF can seek out more similar images to misclassified sample, such as a similar airplane
and white horses, compared to DASCE-SF which seeks out
a dissimilar truck image (giving it a big weight 1.437). It
indicates that the self-supervised feature is more effective for
exploring accurate class-correlation than the supervised feature. Moreover, compared DASCE-Fusion to DASCE-SSF, it is
observed that DASCE-Fusion assigns higher weights to these
two atom-samples (airplane: 0.413 and white horse: 0.275)
than DASCE-SSF (airplane: 0.370 and white horse: 0.268).
This demonstrates that the correlation explored based on fusion
features is more accurate, as it identifies samples highly similar
to the misclassified ones in human vision and assigns them
higher weights.
In addition, the class-correlation weight vectors (W) based
misclassified sample corresponding to Fig. 7 are demonstrated
in Table IV, where the weight W is calculated by (13).
Observably, the categories represented by non-zero weights

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

QI et al.: DASCE: LONG-TAILED DATA AUGMENTATION BASED SPARSE CLASS-CORRELATION EXPLOITATION

4509

Fig. 7. Generates process instances of DASCE. Each row, from top to bottom, delineates the generation process based on discriminative features(DSACE-SF),
inherent features(DSACE-SSF), and fusion features(DSACE-Fusion), respectively. The digits beneath the images denote the category labels. The images in the gray
boxes represent samples similar to the misclassified sample, selected from the dictionary during class-correlation exploration, above which are their corresponding
weights.
TABLE IV
CORRESPONDING TO THE GENERATION PROCESS DEPICTED IN FIG. 7, THIS TABLE DISPLAYS THE CLASS-CORRELATIONS EXPLORED USING DIFFERENT FEATURES

TABLE V
PERFORMANCE COMPARISON OF DASCE AND M2M TRAINED ACROSS A BROADER RANGE OF IMBALANCE RATIOS ON THE CIFAR-10-LT

are largely consistent across various features (all including the
class-1-plane and the class-8-horse), which indicates a similarity in the explored class-correlations across different features.
Combined with the results in Table II, it illustrates that new
samples generated by our model with the fusion feature enable
the model to achieve better performance compared to only with
the supervised feature or the self-supervised feature, since the
fusion feature is more effective for exploring more accurate
class-correlation.
F. Analyses on Different Imbalanced Ratios
In Section IV-B, the experimental results illustrate that
DASCE achieves better performances for high imbalance ratios
(such as ρ = 200, 100). Thus, we also implement the experiments
to observe the performance of DASCE for higher imbalance
ratios on CIFAR-10-LT. Table V shows the experimental results
of ρ = 500, 400, 200 and 100, where the performances of
majority, middle and minority classes are respectively given and

the overall accuracy (Acc) is shown in brackets. When ρ = 500,
the sample numbers of ten classes are 5000, 2506, 1256, 629,
315, 158, 79, 39, 19 and 10. When ρ = 400, the sample numbers
of ten classes are 5000, 2569, 1320, 678, 348, 179, 92, 47, 24
and 12.
From Table V, it is seen that DASCE achieves the better
performance (Acc), with the 2.34%, 0.97%, 2.07% and 2.06%
improvements compared with M2m. Obviously, these improvements are attributed to the classification ability of DASCE for the
minority classes. Specifically, for ρ = 500 and ρ=400, DASCE
improves the accuracy respectively by 11.68% and 3.97% for the
minority classes. Similarly, the 7.01% and 6.73% improvements
are obtained for ρ=200 and ρ=100. These results illustrate that
DASCE is more effective for high imbalanced ratios, especially
for minority classes.
As we know, when the imbalance ratio is higher, the samples
of minority classes are scarcer, which greatly increases the
difficulty of correct classifications, since the deep model may
skew to majority classes more easily. From Table V, it is seen that

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

4510

IEEE TRANSACTIONS ON KNOWLEDGE AND DATA ENGINEERING, VOL. 37, NO. 8, AUGUST 2025

the performance of the minority classes significantly decreases
as the imbalanced ratio increases, and this decrease is greater
than that for the majority and middle classes. It means that the
improvement for the minority classes is more crucial than the
majority and middle classes for higher imbalance ratios. Although our model is inferior to M2m for the majority classes and
competitive for the middle classes, it is very prominent for the
minority classes, and the advantage (Amin ) is much higher than
the deficiency (Amaj ). It demonstrates that DASCE can generate
more representative samples that are benefit for enhancing the
classification ability, since DASCE utilizes class-correlations to
ensure that the generated samples are in the correct distribution
range and suppress the deviation from the target classes.

V. CONCLUSION
In this paper, we propose a new data augmentation method
for long-tailed classification problem. Different from most existing methods, the proposed method deviates from the default
assumption of class-independence, which explores and utilizes
the correlation among classes in data augmentation for the
first time. In the proposed method, a sparse class-correlation
exploration approach is first proposed to explore the potential
class-correlations, and the pivotal seed-samples are constructed
based on the explored class-correlations. Then, the ambiguityfiltered target-samples translation module is proposed to translate the seed-samples into more representative target-samples
by enhancing the class-consistency and suppressing the classdeviation. Moreover, the self-supervised learning is introduced
to capture the inherent feature which is fused with the discriminant feature for exploring more accurate class-correlations. A
lot of experimental results indicate that the proposed method
outperforms compared methods, especially obtaining more obvious improvements for minority classes on higher imbalanced
ratios. In addition, experiments also demonstrate our method
can generate more representative target-samples by utilizing
class-correlation, since a handful of generated samples can
bring obvious performance improvement. It indicates that the
explored class-correlation is considerable and effective for data
augmentation in long-tailed classification.

REFERENCES
[1] J. M. Johnson and T. M. Khoshgoftaar, “Survey on deep learning with
class imbalance,” J. Big Data, vol. 6, no. 1, pp. 1–54, 2019.
[2] H. He and E. A. Garcia, “Learning from imbalanced data,” IEEE Trans.
Knowl. Data Eng., vol. 21, no. 9, pp. 1263–1284, Sep. 2009.
[3] L. Yang, H. Jiang, Q. Song, and J. Guo, “A survey on long-tailed visual
recognition,” Int. J. Comput. Vis., vol. 130, no. 7, pp. 1837–1872, 2022.
[4] Y. Zhang, B. Kang, B. Hooi, S. Yan, and J. Feng, “Deep long-tailed
learning: A survey,” IEEE Trans. Pattern Anal. Mach. Intell., vol. 45, no. 9,
pp. 10795–10816, Sep. 2023.
[5] Y. LeCun, Y. Bengio, and G. Hinton, “Deep learning,” Nature, vol. 521,
no. 7553, pp. 436–444, 2015.
[6] K. He, X. Zhang, S. Ren, and J. Sun, “Deep residual learning for image
recognition,” in Proc. IEEE Conf. Comput. Vis. Pattern Recognit., 2016,
pp. 770–778.
[7] A. Vaswani et al., “Attention is all you need,” in Proc. Adv. Neural Inf.
Process. Syst., 2017, pp. 1–11.

[8] A. Krizhevsky, I. Sutskever, and G. E. Hinton, “ImageNet classification
with deep convolutional neural networks,” in Proc. Adv. Neural Inf. Process. Syst., 2012, pp. 84–90.
[9] C. Dong, C. C. Loy, K. He, and X. Tang, “Image super-resolution using
deep convolutional networks,” IEEE Trans. Pattern Anal. Mach. Intell.,
vol. 38, no. 2, pp. 295–307, Feb. 2016.
[10] S. Ren, K.R. HeGirshick, and J. Sun, “Faster R-CNN: Towards real-time
object detection with region proposal networks,” in Proc. Adv. Neural Inf.
Process. Syst., 2015, pp. 91–99.
[11] J. Devlin, M.-W. Chang, K. Lee, and K. Toutanova, “BERT: Pre-training
of deep bidirectional transformers for language understanding,” 2018,
arXiv: 1810.04805.
[12] Y. Cui, M. Jia, T.-Y. Lin, Y. Song, and S. Belongie, “Class-balanced loss
based on effective number of samples,” in Proc. IEEE/CVF Conf. Comput.
Vis. Pattern Recognit., 2019, pp. 9268–9277.
[13] K. Cao, C. Wei, A. Gaidon, N. Arechiga, and T. Ma, “Learning imbalanced
datasets with label-distribution-aware margin loss,” in Proc. Adv. Neural
Inf. Process. Syst., 2019, pp. 1567–1578.
[14] J. Tan et al., “Equalization loss for long-tailed object recognition,” in Proc.
IEEE/CVF Conf. Comput. Vis. Pattern Recognit., 2020, pp. 11662–11671.
[15] C.-H. Ho, K.-C. Peng, and N. Vasconcelos, “Long-tailed anomaly detection with learnable class names,” 2024, arXiv: 2403.20236.
[16] X. Yuan, L. Xie, and M. Abouelenien, “A regularized ensemble framework
of deep learning for cancer detection from multi-class, imbalanced training
data,” Pattern Recognit., vol. 77, pp. 160–172, 2018.
[17] X. Hao et al., “Producing more with less: A gan-based network attack
detection approach for imbalanced data,” in Proc. IEEE 24th Int. Conf.
Comput. Supported Cooperative Work Des., 2021, pp. 384–390.
[18] T.-Y. Lin, P. Goyal, R. Girshick, K. He, and P. Dollár, “Focal loss for
dense object detection,” in Proc. IEEE Int. Conf. Comput. Vis., 2017,
pp. 2980–2988.
[19] N. V. Chawla, K. W. Bowyer, L. O. Hall, and W. P. Kegelmeyer, “SMOTE:
Synthetic minority over-sampling technique,” J. Artif. Intell. Res., vol. 16,
pp. 321–357, 2002.
[20] H. Zhang, M. Cisse, Y. N. Dauphin, and D. Lopez-Paz, “mixup: Beyond
empirical risk minimization,” 2017, arXiv: 1710.09412.
[21] J. Kim, J. Jeong, and J. Shin, “M2M: Imbalanced classification via majorto-minor translation,” in Proc. IEEE/CVF Conf. Comput. Vis. Pattern
Recognit., 2020, pp. 13896–13905.
[22] W. Ouyang, X. Wang, C. Zhang, and X. Yang, “Factors in finetuning deep
model for object detection with long-tail distribution,” in Proc. IEEE Conf.
Comput. Vis. Pattern Recognit., 2016, pp. 864–873.
[23] B. Kang, Y. Li, S. Xie, Z. Yuan, and J. Feng, “Exploring balanced feature
spaces for representation learning,” in Proc. Int. Conf. Learn. Representations, 2020, pp. 1–15.
[24] B. Zhou, Q. Cui, X.-S. Wei, and Z.-M. Chen, “BBN: Bilateralbranch network with cumulative learning for long-tailed visual recognition,” in Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit., 2020,
pp. 9719–9728.
[25] H. Han, W.-Y. Wang, and B.-H. Mao, “Borderline-smote: A new oversampling method in imbalanced data sets learning,” in Proc. Int. Conf.
Intell. Comput., Springer, 2005, pp. 878–887.
[26] V. Verma et al., “Manifold mixup: Better representations by interpolating
hidden states,” in Proc. Int. Conf. Mach. Learn., 2019, pp. 6438–6447.
[27] S. Yun, D. Han, S. J. Oh, S. Chun, J. Choe, and Y. Yoo, “Cutmix:
Regularization strategy to train strong classifiers with localizable features,”
in Proc. IEEE/CVF Int. Conf. Comput. Vis., 2019, pp. 6023–6032.
[28] H.-P. Chou, S.-C. Chang, J.-Y. Pan, W. Wei, and D.-C. Juan, “Remix:
Rebalanced mixup,” in Proc. Eur. Conf. Comput. Vis. Workshops, Glasgow,
U.K., 2020, pp. 95–110.
[29] F. H. K. D. S. Tanaka and C. Aranha, “Data augmentation using GANs,”
2019, arXiv: 1904.09135.
[30] Y. Zang, C. Huang, and C. C. Loy, “FASA: Feature augmentation and
sampling adaptation for long-tailed instance segmentation,” in Proc.
IEEE/CVF Int. Conf. Comput. Vis., 2021, pp. 3457–3466.
[31] P. Chu, X. Bian, S. Liu, and H. Ling, “Feature space augmentation for
long-tailed data,” in Proc. 16th Eur. Conf. Comput. Vis., Glasgow, U.K.,
2020, pp. 694–710.
[32] D. Liu, L. Zhao, Y. Wang, and J. Kato, “Learn from each other to classify better: Cross-layer mutual attention learning for fine-grained visual
classification,” Pattern Recognit., vol. 140, 2023, Art. no. 109550.
[33] P.-Y. Chou, Y.-Y. Kao, and C.-H. Lin, “Fine-grained visual classification
with high-temperature refinement and background suppression,” 2023,
arXiv: 2303.06442.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

QI et al.: DASCE: LONG-TAILED DATA AUGMENTATION BASED SPARSE CLASS-CORRELATION EXPLOITATION

[34] W. Wang, N. Sebe, and B. Lepri, “Rethinking the learning paradigm for
facial expression recognition,” 2022, arXiv: 2209.15402.
[35] Y. Wang, W. Gan, J. Yang, W. Wu, and J. Yan, “Dynamic curriculum
learning for imbalanced data classification,” in Proc. IEEE/CVF Int. Conf.
Comput. Vis., 2019, pp. 5017–5026.
[36] D. Wang, Y. Liu, L. Fang, F. Shang, Y. Liu, and H. Liu, “Balanced gradient
penalty improves deep long-tailed learning,” in Proc. 30th ACM Int. Conf.
Multimedia, 2022, pp. 5093–5101.
[37] K. Tang, J. Huang, and H. Zhang, “Long-tailed classification by keeping
the good and removing the bad momentum causal effect,” in Proc. Adv.
Neural Inf. Process. Syst., 2020, pp. 1513–1524.
[38] Y. Cui, Y. Song, C. Sun, A. Howard, and S. Belongie, “Large scale finegrained categorization and domain-specific transfer learning,” in Proc.
IEEE Conf. Comput. Vis. Pattern Recognit., 2018, pp. 4109–4118.
[39] J. A. Tropp and A. C. Gilbert, “Signal recovery from random measurements
via orthogonal matching pursuit,” IEEE Trans. Inf. Theory, vol. 53, no. 12,
pp. 4655–4666, Dec. 2007.
[40] T. Chen, S. Kornblith, M. Norouzi, and G. Hinton, “A simple framework
for contrastive learning of visual representations,” in Proc. Int. Conf. Mach.
Learn., 2020, pp. 1597–1607.
[41] A. Krizhevsky and H. Geoffrey, “Learning multiple layers of features
from tiny images,” Univ. Toronto, Tech. Rep., 2009. [Online]. Available:
https://www.cs.utoronto.ca/∼kriz/learning-features-2009-TR.pdf
[42] M. Ren, W. Zeng, B. Yang, and R. Urtasun, “Learning to reweight examples for robust deep learning,” in Proc. Int. Conf. Mach. Learn., 2018,
pp. 4334–4343.
[43] J. Shu et al., “Meta-weight-net: Learning an explicit mapping for sample
weighting,” in Proc. Adv. Neural Inf. Process. Syst., 2019, pp. 1919–1930.
[44] M. A. Jamal, M. Brown, M.-H. Yang, L. Wang, and B. Gong, “Rethinking
class-balanced methods for long-tailed visual recognition from a domain
adaptation perspective,” in Proc. IEEE/CVF Conf. Comput. Vis. Pattern
Recognit., 2020, pp. 7610–7619.
[45] S. Park, J. Lim, Y. Jeon, and J. Y. Choi, “Influence-balanced loss for
imbalanced visual classification,” in Proc. IEEE/CVF Int. Conf. Comput.
Vis., 2021, pp. 735–744.
[46] S. Yu, J. Guo, R. Zhang, Y. Fan, Z. Wang, and X. Cheng, “A re-balancing
strategy for class-imbalanced classification based on instance difficulty,”
in Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit., 2022, pp. 70–79.
[47] Y. Ma, L. Jiao, F. Liu, S. Yang, X. Liu, and L. Li, “Curvature-balanced feature manifold learning for long-tailed classification,” in Proc. IEEE/CVF
Conf. Comput. Vis. Pattern Recognit., 2023, pp. 15824–15835.
[48] K. He, X. Zhang, S. Ren, and J. Sun, “Identity mappings in deep residual networks,” in Proc. 14th Eur. Conf. Comput. Vis., Amsterdam, The
Netherlands, 2016, pp. 630–645.
[49] L. V. der Maaten and G. Hinton, “Visualizing data using t-SNE,” J. Mach.
Learn. Res., vol. 9, no. 11, pp. 2579–2605, 2008.

Mengnan Qi (Student Member, IEEE) received the
BS degree in intelligent science and technology, in
2020, and the MS degree in computer science and
technology, in 2023, both from Xidian University,
Xi’an, China. He is currently working toward the
PhD degree in electronic science and technology with
Xidian University. His research interests include imbalanced learning, weakly supervised learning, and
FPGA-based neural network acceleration.

Shasha Mao (Member, IEEE) received the PhD degree in circuits and systems from the Key Laboratory
of Intelligent Perception and Image Understanding
of Ministry of Education, Xidian University, Xi’an,
China, in 2014. She is currently an associate professor with the School of Artificial Intelligence, Xidian
University. From 2014 to 2018, she was a research
fellow with Nanyang Technological University, Singapore, and the Singapore University of Technology
and Design, Singapore. Her current research interests
include ensemble learning, deep learning, imbalance
learning, low-rank and sparse matrix factorization, facial expression recognition,
and abnormal detection.

4511

Yimeng Zhang (Member, IEEE) received the BE
degree in microelectronics from Tsinghua University, Beijing, China, in 2001, and the ME and PhD
degrees in system large-scale integrated (LSI) design
from Waseda University, Fukuoka, Japan, in 2007
and 2012, respectively. He is currently working as a
professor with the School of Microelectronics, Xidian
University, Xi’an, China. His current research interests include power electronics based on wide bandgap
semiconductors and hardware acceleration for AI.

Jing Gu (Member, IEEE) received the BS in electronic and information engineering and the MS degrees in signal and information processing from Xi’an
University of Technology, Xi’an, China, in 2007 and
2010, respectively, and the PhD degree from Xidian
University, Xi’an, China, in 2016. She is currently
an associate professor with the Key Laboratory of
Intelligent Perception and Image Understanding of
the Ministry of Education, Xidian University. Her
research interests include image processing, video
analysis, and machine learning.

Shuiping Gou (Senior Member, IEEE) received the
BS and MS degrees in computer science and technology and the PhD degree in pattern recognition and
intelligent systems from Xidian University, Xi’an,
China, in 2000, 2003, and 2008, respectively. She
is currently a professor with the Key Laboratory of
Intelligent Perception and Image Understanding of
Ministry of Education of China, Xidian University.
Her research interests include machine learning, data
mining, and remote sensing image analysis.

Licheng Jiao (Fellow, IEEE) received the BS degree
in high voltage from Shanghai Jiaotong University,
Shanghai, China, in 1982, and the MS and PhD
degrees in electronic engineering from Xi’an Jiaotong University, Xi’an, China, in 1984 and 1990,
respectively. Since 1992, he has been a distinguished
professor with the School of Electronic Engineering,
Xidian University, Xi’an, China, where he is currently
the director of the Key Laboratory of Intelligent Perception and Image Understanding of the Ministry of
Education of China. He is the HuaShan Outstanding
professor with Xidian University. His research interests include machine learning, deep learning, natural computation, remote sensing, image processing, and
intelligent information processing. He is a foreign member of the Academia
European in London, U.K. and the Russian Academy of Natural Science in
Saint Petersburg, Russia. He is the Chairman of the Awards and Recognition
Committee, the Vice Board Chairperson of the Chinese Association of Artificial
Intelligence in Beijing, China, the Fellow of the Institution of Engineering and
Technology in London, U.K., the Chinese Association for Artificial Intelligence,
the Chinese Institute of Electronics, the China Computer Federation, and the
Chinese Association of Automation in Beijing, China, a Councilor of the Chinese
Institute of Electronics in Beijing, China, a Committee Member of the Chinese
Committee of Neural Networks in Beijing, China, and an Expert of the Academic
Degrees Committee of the State Council in Beijing, China.
Yuming Zhang (Senior Member, IEEE) received
the BS degree in microelectronics from Tsinghua
University, Beijing, China, in 1989, the MS degree
in microelectronics from Xidian University, Xi’an,
China, in 1992, and the PhD degree in microelectronics from Xi’an Jiaotong University, Xi’an, China,
in 1998. He is currently working with the School of
Microelectronics, Xidian University, as the dean and
a professor. His current research interests include the
materials, devices and circuit applications of silicon
carbide semiconductors.

Authorized licensed use limited to: Technical University of Denmark DTU Library. Downloaded on July 31,2025 at 06:27:51 UTC from IEEE Xplore. Restrictions apply.

