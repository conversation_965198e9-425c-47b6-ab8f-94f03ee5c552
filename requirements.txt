# Trust-Calibrated Transformer Requirements
# Core deep learning framework
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Scientific computing
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# Machine learning utilities
scikit-learn>=1.0.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Experiment tracking and logging
wandb>=0.15.0
tensorboard>=2.10.0
tqdm>=4.64.0

# Statistical analysis
statsmodels>=0.13.0

# System monitoring
psutil>=5.8.0

# Configuration management
pyyaml>=6.0
omegaconf>=2.2.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0
flake8>=5.0.0
mypy>=0.991

# Documentation
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0

# Jupyter notebooks (for analysis)
jupyter>=1.0.0
ipykernel>=6.15.0

# Additional utilities
click>=8.0.0
rich>=12.0.0
pathlib2>=2.3.0
