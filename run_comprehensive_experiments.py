#!/usr/bin/env python3
"""
Comprehensive Experiments Runner

Main script to reproduce all experimental results from the paper:
"Trust-Calibrated Transformer for Dynamic Multi-Source Data Fusion with Uncertainty Quantification"

This script runs:
1. Main performance comparison across all datasets and baseline methods
2. Ablation studies for each framework component  
3. Robustness analysis under various challenging conditions
4. Statistical significance testing
5. Computational efficiency analysis
6. Generation of all tables and figures

Usage:
    python run_comprehensive_experiments.py --config paper_reproduction
    python run_comprehensive_experiments.py --config quick_test
    python run_comprehensive_experiments.py --config custom --datasets autonomous_vehicle medical_diagnosis
"""

import argparse
import logging
import os
import sys
import time
from datetime import datetime
import torch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.experiments import ComprehensiveExperimentRunner
from src.experiments.config_manager import ConfigManager, PAPER_REPRODUCTION_CONFIG, QUICK_TEST_CONFIG
from src.experiments.results_generator import ResultsGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('experiment.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='Run comprehensive experiments for Trust-Calibrated Transformer',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Full paper reproduction (takes ~8-12 hours)
  python run_comprehensive_experiments.py --config paper_reproduction
  
  # Quick test (takes ~30 minutes)
  python run_comprehensive_experiments.py --config quick_test
  
  # Custom configuration
  python run_comprehensive_experiments.py --config custom --datasets autonomous_vehicle medical_diagnosis --baselines "MLP Fusion" "Standard Transformer"
  
  # Ablation study only
  python run_comprehensive_experiments.py --config ablation
  
  # Robustness testing only
  python run_comprehensive_experiments.py --config robustness
        """
    )
    
    parser.add_argument(
        '--config',
        type=str,
        choices=['paper_reproduction', 'quick_test', 'ablation', 'robustness', 'efficiency', 'custom'],
        default='quick_test',
        help='Experiment configuration to use'
    )
    
    parser.add_argument(
        '--datasets',
        nargs='+',
        help='Datasets to test (for custom config)'
    )
    
    parser.add_argument(
        '--baselines',
        nargs='+',
        help='Baseline methods to compare against (for custom config)'
    )
    
    parser.add_argument(
        '--device',
        type=str,
        default='auto',
        help='Device to use (auto, cpu, cuda)'
    )
    
    parser.add_argument(
        '--results-dir',
        type=str,
        default='experiment_results',
        help='Directory to save results'
    )
    
    parser.add_argument(
        '--n-jobs',
        type=int,
        default=1,
        help='Number of parallel jobs (not implemented yet)'
    )
    
    parser.add_argument(
        '--skip-baselines',
        action='store_true',
        help='Skip baseline comparisons (faster testing)'
    )
    
    parser.add_argument(
        '--generate-only',
        action='store_true',
        help='Only generate tables and figures from existing results'
    )
    
    parser.add_argument(
        '--seed',
        type=int,
        default=42,
        help='Random seed for reproducibility'
    )
    
    return parser.parse_args()


def setup_environment(args):
    """Setup experimental environment"""
    
    # Set random seeds
    torch.manual_seed(args.seed)
    import numpy as np
    np.random.seed(args.seed)
    
    # Create results directory
    os.makedirs(args.results_dir, exist_ok=True)
    
    # Log system information
    logger.info("=" * 80)
    logger.info("TRUST-CALIBRATED TRANSFORMER COMPREHENSIVE EXPERIMENTS")
    logger.info("=" * 80)
    logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"Configuration: {args.config}")
    logger.info(f"Results directory: {args.results_dir}")
    logger.info(f"Device: {args.device}")
    logger.info(f"Random seed: {args.seed}")
    
    # Check CUDA availability
    if torch.cuda.is_available():
        logger.info(f"CUDA available: {torch.cuda.get_device_name()}")
        logger.info(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        logger.info("CUDA not available, using CPU")
    
    logger.info("=" * 80)


def get_experiment_config(args):
    """Get experiment configuration based on arguments"""
    
    if args.config == 'paper_reproduction':
        config = ConfigManager.get_paper_reproduction_config()
    elif args.config == 'quick_test':
        config = ConfigManager.get_quick_test_config()
    elif args.config == 'ablation':
        config = ConfigManager.get_ablation_study_config()
    elif args.config == 'robustness':
        config = ConfigManager.get_robustness_test_config()
    elif args.config == 'efficiency':
        config = ConfigManager.get_computational_efficiency_config()
    elif args.config == 'custom':
        config = ConfigManager.create_custom_config(
            experiment_type='full',
            datasets=args.datasets,
            baselines=args.baselines
        )
    else:
        raise ValueError(f"Unknown config: {args.config}")
    
    # Override with command line arguments
    config.device = args.device
    
    if args.skip_baselines:
        config.baseline_methods = []
    
    # Validate configuration
    if not ConfigManager.validate_config(config):
        raise ValueError("Invalid configuration")
    
    # Log configuration summary
    logger.info("EXPERIMENT CONFIGURATION:")
    logger.info("-" * 40)
    logger.info(ConfigManager.get_config_summary(config))
    logger.info("-" * 40)
    
    return config


def run_experiments(config, args):
    """Run the comprehensive experiments"""
    
    logger.info("Initializing experiment runner...")
    
    # Initialize experiment runner
    runner = ComprehensiveExperimentRunner(
        config=config,
        device=args.device,
        n_jobs=args.n_jobs
    )
    
    # Run all experiments
    logger.info("Starting comprehensive experiments...")
    start_time = time.time()
    
    try:
        results = runner.run_all_experiments(
            save_intermediate=True,
            results_dir=args.results_dir
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        logger.info("=" * 80)
        logger.info("✅ EXPERIMENTS COMPLETED SUCCESSFULLY!")
        logger.info(f"Total execution time: {total_time/3600:.2f} hours")
        logger.info(f"Results saved to: {args.results_dir}")
        logger.info("=" * 80)
        
        return results
        
    except Exception as e:
        logger.error(f"❌ EXPERIMENTS FAILED: {e}")
        raise


def generate_outputs(args):
    """Generate tables and figures from results"""
    
    logger.info("Generating tables and figures...")
    
    try:
        # Initialize results generator
        generator = ResultsGenerator(results_dir=args.results_dir)
        
        # Generate all outputs
        generator.generate_paper_ready_outputs(
            output_dir=os.path.join(args.results_dir, 'paper_outputs')
        )
        
        logger.info("✅ Tables and figures generated successfully!")
        logger.info(f"Paper-ready outputs saved to: {os.path.join(args.results_dir, 'paper_outputs')}")
        
    except Exception as e:
        logger.error(f"❌ Output generation failed: {e}")
        raise


def main():
    """Main execution function"""
    
    # Parse arguments
    args = parse_arguments()
    
    # Setup environment
    setup_environment(args)
    
    try:
        if args.generate_only:
            # Only generate outputs from existing results
            generate_outputs(args)
        else:
            # Get configuration
            config = get_experiment_config(args)
            
            # Run experiments
            results = run_experiments(config, args)
            
            # Generate outputs
            generate_outputs(args)
        
        # Final summary
        logger.info("\n" + "=" * 80)
        logger.info("🎉 ALL TASKS COMPLETED SUCCESSFULLY!")
        logger.info("=" * 80)
        logger.info("Generated files:")
        logger.info(f"  • Experiment results: {args.results_dir}/complete_results.json")
        logger.info(f"  • LaTeX tables: {args.results_dir}/paper_outputs/tables/")
        logger.info(f"  • PDF figures: {args.results_dir}/paper_outputs/figures/")
        logger.info(f"  • Experiment log: experiment.log")
        logger.info("=" * 80)
        
    except KeyboardInterrupt:
        logger.info("\n⚠️ Experiments interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
