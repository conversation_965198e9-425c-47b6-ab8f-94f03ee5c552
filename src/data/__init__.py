"""
Data Pipeline and Preprocessing

Implements data loaders and preprocessing pipelines for all eight datasets
mentioned in the paper:

1. Autonomous Vehicles - Multi-sensor fusion (LiDAR, camera, radar, GPS, IMU)
2. Medical Diagnosis - Multi-modal clinical data (lab tests, imaging, vitals)
3. Environmental Monitoring - Distributed sensor networks (temperature, humidity, air quality)
4. Financial Markets - Multi-source trading data (price, volume, sentiment, news)
5. IoT Networks - Heterogeneous sensor data (smart home, industrial IoT)
6. Weather Forecasting - Multi-station meteorological data
7. Traffic Management - Multi-camera and sensor traffic data
8. Energy Systems - Smart grid multi-sensor data
"""

from .datasets import (
    AutonomousVehicleDataset,
    MedicalDiagnosisDataset,
    EnvironmentalMonitoringDataset,
    FinancialMarketsDataset,
    IoTNetworksDataset,
    WeatherForecastingDataset,
    TrafficManagementDataset,
    EnergySystemsDataset
)

from .data_loader import MultiSourceDataLoader
from .preprocessing import DataPreprocessor
from .synthetic_generator import SyntheticDataGenerator

__all__ = [
    # Dataset classes
    'AutonomousVehicleDataset',
    'MedicalDiagnosisDataset', 
    'EnvironmentalMonitoringDataset',
    'FinancialMarketsDataset',
    'IoTNetworksDataset',
    'WeatherForecastingDataset',
    'TrafficManagementDataset',
    'EnergySystemsDataset',
    
    # Data utilities
    'MultiSourceDataLoader',
    'DataPreprocessor',
    'SyntheticDataGenerator'
]
