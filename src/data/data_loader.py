"""
Multi-Source Data Loader

Provides unified data loading interface for all datasets with support for
different batch sampling strategies and data augmentation techniques.
"""

import torch
from torch.utils.data import DataLoader, Dataset, Sampler
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
import logging
from collections import defaultdict

from .datasets import (
    AutonomousVehicleDataset, MedicalDiagnosisDataset, EnvironmentalMonitoringDataset,
    FinancialMarketsDataset, IoTNetworksDataset, WeatherForecastingDataset,
    TrafficManagementDataset, EnergySystemsDataset
)

logger = logging.getLogger(__name__)


class MultiSourceDataLoader:
    """
    Unified Data Loader for Multi-Source Datasets
    
    Provides consistent interface for loading all eight datasets
    with support for different reliability patterns and augmentation.
    """
    
    DATASET_CLASSES = {
        'autonomous_vehicle': AutonomousVehicleDataset,
        'medical_diagnosis': MedicalDiagnosisDataset,
        'environmental_monitoring': EnvironmentalMonitoringDataset,
        'financial_markets': FinancialMarketsDataset,
        'iot_networks': IoTNetworksDataset,
        'weather_forecasting': WeatherForecastingDataset,
        'traffic_management': TrafficManagementDataset,
        'energy_systems': EnergySystemsDataset
    }
    
    def __init__(
        self,
        dataset_name: str,
        data_dir: str = 'data',
        batch_size: int = 32,
        num_workers: int = 4,
        reliability_pattern: str = 'dynamic',
        noise_level: float = 0.1
    ):
        self.dataset_name = dataset_name
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.reliability_pattern = reliability_pattern
        self.noise_level = noise_level
        
        if dataset_name not in self.DATASET_CLASSES:
            raise ValueError(f"Unknown dataset: {dataset_name}. "
                           f"Available: {list(self.DATASET_CLASSES.keys())}")
        
        self.dataset_class = self.DATASET_CLASSES[dataset_name]
        
        # Initialize datasets
        self.datasets = {}
        self.data_loaders = {}
        
        logger.info(f"Initialized MultiSourceDataLoader for {dataset_name}")
    
    def get_data_loaders(
        self,
        splits: List[str] = ['train', 'val', 'test'],
        shuffle: bool = True,
        drop_last: bool = True
    ) -> Dict[str, DataLoader]:
        """
        Get data loaders for specified splits
        
        Args:
            splits: List of data splits to create loaders for
            shuffle: Whether to shuffle training data
            drop_last: Whether to drop last incomplete batch
            
        Returns:
            Dictionary mapping split names to DataLoader objects
        """
        data_loaders = {}
        
        for split in splits:
            # Create dataset
            dataset = self.dataset_class(
                data_dir=self.data_dir,
                split=split,
                reliability_pattern=self.reliability_pattern,
                noise_level=self.noise_level
            )
            
            # Create data loader
            shuffle_split = shuffle and (split == 'train')
            
            data_loader = DataLoader(
                dataset,
                batch_size=self.batch_size,
                shuffle=shuffle_split,
                num_workers=self.num_workers,
                drop_last=drop_last,
                collate_fn=self._collate_fn,
                pin_memory=torch.cuda.is_available()
            )
            
            data_loaders[split] = data_loader
            self.datasets[split] = dataset
            
            logger.info(f"Created {split} loader: {len(dataset)} samples, "
                       f"{len(data_loader)} batches")
        
        self.data_loaders = data_loaders
        return data_loaders
    
    def _collate_fn(self, batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Custom collate function for multi-source data
        
        Args:
            batch: List of dataset items
            
        Returns:
            Batched data dictionary
        """
        batch_size = len(batch)
        n_sources = len(batch[0]['source_data'])
        
        # Collate source data
        source_data = []
        for source_idx in range(n_sources):
            source_batch = torch.stack([
                item['source_data'][source_idx] for item in batch
            ])
            source_data.append(source_batch)
        
        # Collate targets
        targets = torch.stack([item['targets'] for item in batch])
        
        # Collate reliability patterns
        reliability = torch.stack([item['reliability'] for item in batch])
        
        # Other metadata
        source_names = batch[0]['source_names']
        indices = torch.tensor([item['index'] for item in batch])
        
        return {
            'source_data': source_data,
            'targets': targets,
            'reliability': reliability,
            'source_names': source_names,
            'indices': indices,
            'batch_size': batch_size
        }
    
    def get_dataset_info(self) -> Dict[str, Any]:
        """Get information about the dataset"""
        if not self.datasets:
            # Create a sample dataset to get info
            sample_dataset = self.dataset_class(
                data_dir=self.data_dir,
                split='train',
                reliability_pattern=self.reliability_pattern,
                noise_level=self.noise_level
            )
        else:
            sample_dataset = self.datasets['train']
        
        # Get sample to determine dimensions
        sample = sample_dataset[0]
        
        input_dims = [data.shape[-1] for data in sample['source_data']]
        output_dim = sample['targets'].shape[-1]
        n_sources = len(sample['source_data'])
        source_names = sample['source_names']
        
        return {
            'dataset_name': self.dataset_name,
            'n_sources': n_sources,
            'source_names': source_names,
            'input_dims': input_dims,
            'output_dim': output_dim,
            'total_samples': {split: len(dataset) for split, dataset in self.datasets.items()},
            'reliability_pattern': self.reliability_pattern,
            'noise_level': self.noise_level
        }
    
    def create_calibration_loader(
        self,
        calibration_size: int = 1000,
        from_split: str = 'val'
    ) -> DataLoader:
        """
        Create a separate calibration data loader for conformal prediction
        
        Args:
            calibration_size: Number of samples for calibration
            from_split: Which split to sample from
            
        Returns:
            Calibration data loader
        """
        if from_split not in self.datasets:
            raise ValueError(f"Split {from_split} not available. "
                           f"Available: {list(self.datasets.keys())}")
        
        source_dataset = self.datasets[from_split]
        
        # Create subset for calibration
        indices = np.random.choice(
            len(source_dataset), 
            size=min(calibration_size, len(source_dataset)),
            replace=False
        )
        
        calibration_dataset = torch.utils.data.Subset(source_dataset, indices)
        
        calibration_loader = DataLoader(
            calibration_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=self.num_workers,
            collate_fn=self._collate_fn,
            pin_memory=torch.cuda.is_available()
        )
        
        logger.info(f"Created calibration loader: {len(calibration_dataset)} samples")
        return calibration_loader
    
    @staticmethod
    def get_all_datasets_info() -> Dict[str, Dict[str, Any]]:
        """Get information about all available datasets"""
        datasets_info = {}
        
        for dataset_name in MultiSourceDataLoader.DATASET_CLASSES.keys():
            try:
                loader = MultiSourceDataLoader(dataset_name)
                loader.get_data_loaders(['train'])
                info = loader.get_dataset_info()
                datasets_info[dataset_name] = info
            except Exception as e:
                logger.warning(f"Failed to get info for {dataset_name}: {e}")
                datasets_info[dataset_name] = {'error': str(e)}
        
        return datasets_info


class ReliabilityAwareSampler(Sampler):
    """
    Custom sampler that considers source reliability patterns
    for more balanced training across different reliability scenarios.
    """
    
    def __init__(
        self,
        dataset: Dataset,
        reliability_bins: int = 5,
        samples_per_bin: Optional[int] = None
    ):
        self.dataset = dataset
        self.reliability_bins = reliability_bins
        self.samples_per_bin = samples_per_bin
        
        # Analyze reliability patterns in dataset
        self._analyze_reliability_patterns()
    
    def _analyze_reliability_patterns(self):
        """Analyze and bin samples by reliability patterns"""
        
        # Sample reliability patterns from dataset
        reliability_samples = []
        sample_indices = np.random.choice(
            len(self.dataset), 
            size=min(1000, len(self.dataset)), 
            replace=False
        )
        
        for idx in sample_indices:
            sample = self.dataset[idx]
            reliability = sample['reliability']
            # Use mean reliability as binning criterion
            mean_reliability = torch.mean(reliability).item()
            reliability_samples.append((idx, mean_reliability))
        
        # Create bins based on reliability levels
        reliability_values = [r for _, r in reliability_samples]
        bin_edges = np.linspace(min(reliability_values), max(reliability_values), 
                               self.reliability_bins + 1)
        
        # Assign samples to bins
        self.reliability_bins_data = defaultdict(list)
        for idx, reliability in reliability_samples:
            bin_idx = np.digitize(reliability, bin_edges) - 1
            bin_idx = max(0, min(bin_idx, self.reliability_bins - 1))
            self.reliability_bins_data[bin_idx].append(idx)
        
        logger.info(f"Reliability-aware sampler: {len(self.reliability_bins_data)} bins")
        for bin_idx, samples in self.reliability_bins_data.items():
            logger.info(f"  Bin {bin_idx}: {len(samples)} samples")
    
    def __iter__(self):
        """Generate sample indices with reliability-aware sampling"""
        
        if self.samples_per_bin is None:
            # Sample proportionally from each bin
            total_samples = len(self.dataset)
            samples_per_bin = total_samples // self.reliability_bins
        else:
            samples_per_bin = self.samples_per_bin
        
        sampled_indices = []
        
        for bin_idx, bin_samples in self.reliability_bins_data.items():
            if len(bin_samples) == 0:
                continue
            
            # Sample from this bin
            n_samples = min(samples_per_bin, len(bin_samples))
            bin_indices = np.random.choice(bin_samples, size=n_samples, replace=True)
            sampled_indices.extend(bin_indices)
        
        # Shuffle the final indices
        np.random.shuffle(sampled_indices)
        return iter(sampled_indices)
    
    def __len__(self):
        if self.samples_per_bin is None:
            return len(self.dataset)
        else:
            return self.samples_per_bin * self.reliability_bins


def create_all_data_loaders(
    dataset_names: List[str],
    data_dir: str = 'data',
    batch_size: int = 32,
    num_workers: int = 4,
    reliability_pattern: str = 'dynamic'
) -> Dict[str, Dict[str, DataLoader]]:
    """
    Create data loaders for multiple datasets
    
    Args:
        dataset_names: List of dataset names to create loaders for
        data_dir: Data directory
        batch_size: Batch size
        num_workers: Number of worker processes
        reliability_pattern: Reliability pattern type
        
    Returns:
        Nested dictionary: {dataset_name: {split: DataLoader}}
    """
    all_loaders = {}
    
    for dataset_name in dataset_names:
        logger.info(f"Creating loaders for {dataset_name}")
        
        loader = MultiSourceDataLoader(
            dataset_name=dataset_name,
            data_dir=data_dir,
            batch_size=batch_size,
            num_workers=num_workers,
            reliability_pattern=reliability_pattern
        )
        
        data_loaders = loader.get_data_loaders(['train', 'val', 'test'])
        all_loaders[dataset_name] = data_loaders
    
    return all_loaders
