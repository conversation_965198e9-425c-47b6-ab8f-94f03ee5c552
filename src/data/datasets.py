"""
Dataset Classes for Multi-Source Data Fusion

Implements dataset classes for all eight application domains mentioned in the paper.
Each dataset provides multi-source data with varying reliability patterns.
"""

import torch
from torch.utils.data import Dataset
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import logging
import os
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class MultiSourceDataset(Dataset, ABC):
    """
    Abstract base class for multi-source datasets
    
    Provides common interface for all dataset implementations
    with support for dynamic source reliability patterns.
    """
    
    def __init__(
        self,
        data_dir: str,
        split: str = 'train',
        sequence_length: int = 100,
        reliability_pattern: str = 'dynamic',
        noise_level: float = 0.1
    ):
        self.data_dir = data_dir
        self.split = split
        self.sequence_length = sequence_length
        self.reliability_pattern = reliability_pattern
        self.noise_level = noise_level
        
        # Load data
        self.data = self._load_data()
        self.source_names = self._get_source_names()
        self.n_sources = len(self.source_names)
        
        # Generate reliability patterns
        self.reliability_patterns = self._generate_reliability_patterns()
        
    @abstractmethod
    def _load_data(self) -> Dict[str, Any]:
        """Load dataset-specific data"""
        pass
    
    @abstractmethod
    def _get_source_names(self) -> List[str]:
        """Get names of data sources"""
        pass
    
    @abstractmethod
    def _get_sample(self, idx: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        """Get a single sample (source_data, target)"""
        pass
    
    def __len__(self) -> int:
        return len(self.data['targets'])
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """Get dataset item with source data and targets"""
        source_data, targets = self._get_sample(idx)
        
        # Apply reliability-based noise
        source_data = self._apply_reliability_noise(source_data, idx)
        
        return {
            'source_data': source_data,
            'targets': targets,
            'reliability': self.reliability_patterns[idx % len(self.reliability_patterns)],
            'source_names': self.source_names,
            'index': idx
        }
    
    def _generate_reliability_patterns(self) -> List[torch.Tensor]:
        """Generate dynamic reliability patterns for sources"""
        n_patterns = min(1000, len(self))
        patterns = []
        
        for i in range(n_patterns):
            if self.reliability_pattern == 'static':
                # Static reliability (same for all timesteps)
                reliability = torch.rand(self.n_sources) * 0.5 + 0.5  # [0.5, 1.0]
            elif self.reliability_pattern == 'dynamic':
                # Dynamic reliability with temporal variation
                base_reliability = torch.rand(self.n_sources) * 0.4 + 0.4  # [0.4, 0.8]
                variation = torch.sin(torch.tensor(i / 50.0)) * 0.2  # Sinusoidal variation
                reliability = torch.clamp(base_reliability + variation, 0.1, 1.0)
            elif self.reliability_pattern == 'degrading':
                # Gradually degrading reliability
                degradation = torch.tensor(i / n_patterns) * 0.3
                reliability = torch.clamp(torch.ones(self.n_sources) - degradation, 0.2, 1.0)
            else:
                # Random reliability
                reliability = torch.rand(self.n_sources)
            
            patterns.append(reliability)
        
        return patterns
    
    def _apply_reliability_noise(
        self, 
        source_data: List[torch.Tensor], 
        idx: int
    ) -> List[torch.Tensor]:
        """Apply noise based on source reliability"""
        reliability = self.reliability_patterns[idx % len(self.reliability_patterns)]
        
        noisy_data = []
        for i, data in enumerate(source_data):
            # Noise level inversely proportional to reliability
            noise_scale = self.noise_level * (2.0 - reliability[i])
            noise = torch.randn_like(data) * noise_scale
            noisy_data.append(data + noise)
        
        return noisy_data


class AutonomousVehicleDataset(MultiSourceDataset):
    """
    Autonomous Vehicle Multi-Sensor Dataset
    
    Simulates multi-sensor fusion for autonomous driving:
    - LiDAR: 3D point cloud features
    - Camera: Visual features  
    - Radar: Range/velocity measurements
    - GPS: Position coordinates
    - IMU: Acceleration/gyroscope data
    
    Target: Vehicle trajectory prediction
    """
    
    def __init__(self, data_dir: str, **kwargs):
        super().__init__(data_dir, **kwargs)
        
    def _load_data(self) -> Dict[str, Any]:
        """Load or generate autonomous vehicle data"""
        # For demonstration, generate synthetic data
        # In practice, this would load real sensor data
        
        n_samples = 10000 if self.split == 'train' else 2000
        
        # Generate synthetic multi-sensor data
        np.random.seed(42 if self.split == 'train' else 123)
        
        # LiDAR features (64-dimensional point cloud features)
        lidar_data = np.random.randn(n_samples, 64) * 2.0
        
        # Camera features (128-dimensional visual features)
        camera_data = np.random.randn(n_samples, 128) * 1.5
        
        # Radar data (16-dimensional range/velocity)
        radar_data = np.random.randn(n_samples, 16) * 3.0
        
        # GPS coordinates (4-dimensional: lat, lon, alt, accuracy)
        gps_data = np.random.randn(n_samples, 4) * 0.5
        
        # IMU data (12-dimensional: 3-axis accel + 3-axis gyro + 6 derived)
        imu_data = np.random.randn(n_samples, 12) * 1.0
        
        # Target: Next position (x, y coordinates)
        # Combine information from all sensors with some correlation
        combined_signal = (lidar_data[:, :2] * 0.3 + 
                          camera_data[:, :2] * 0.3 + 
                          radar_data[:, :2] * 0.2 + 
                          gps_data[:, :2] * 0.2)
        targets = combined_signal + np.random.randn(n_samples, 2) * 0.1
        
        return {
            'lidar': torch.FloatTensor(lidar_data),
            'camera': torch.FloatTensor(camera_data),
            'radar': torch.FloatTensor(radar_data),
            'gps': torch.FloatTensor(gps_data),
            'imu': torch.FloatTensor(imu_data),
            'targets': torch.FloatTensor(targets)
        }
    
    def _get_source_names(self) -> List[str]:
        return ['lidar', 'camera', 'radar', 'gps', 'imu']
    
    def _get_sample(self, idx: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        source_data = [
            self.data['lidar'][idx],
            self.data['camera'][idx],
            self.data['radar'][idx],
            self.data['gps'][idx],
            self.data['imu'][idx]
        ]
        targets = self.data['targets'][idx]
        return source_data, targets


class MedicalDiagnosisDataset(MultiSourceDataset):
    """
    Medical Diagnosis Multi-Modal Dataset
    
    Simulates multi-modal clinical data fusion:
    - Lab Tests: Blood work, biomarkers
    - Imaging: Radiological features
    - Vitals: Heart rate, blood pressure, temperature
    - History: Patient history features
    - Symptoms: Symptom severity scores
    - Genetics: Genetic risk factors
    - Demographics: Age, gender, BMI
    
    Target: Diagnosis probability scores
    """
    
    def _load_data(self) -> Dict[str, Any]:
        n_samples = 8000 if self.split == 'train' else 1600
        np.random.seed(42 if self.split == 'train' else 123)
        
        # Lab tests (32 different tests)
        lab_tests = np.random.randn(n_samples, 32) * 2.0
        
        # Imaging features (64-dimensional radiological features)
        imaging = np.random.randn(n_samples, 64) * 1.5
        
        # Vital signs (8-dimensional)
        vitals = np.random.randn(n_samples, 8) * 1.0
        
        # Patient history (24-dimensional)
        history = np.random.randn(n_samples, 24) * 1.2
        
        # Symptom scores (16-dimensional)
        symptoms = np.random.randn(n_samples, 16) * 1.8
        
        # Genetic factors (20-dimensional)
        genetics = np.random.randn(n_samples, 20) * 0.8
        
        # Demographics (6-dimensional)
        demographics = np.random.randn(n_samples, 6) * 1.0
        
        # Target: Disease risk score (3 classes)
        combined_signal = (lab_tests[:, :3] * 0.25 + 
                          imaging[:, :3] * 0.25 + 
                          vitals[:, :3] * 0.15 + 
                          symptoms[:, :3] * 0.2 + 
                          genetics[:, :3] * 0.15)
        targets = combined_signal + np.random.randn(n_samples, 3) * 0.2
        
        return {
            'lab_tests': torch.FloatTensor(lab_tests),
            'imaging': torch.FloatTensor(imaging),
            'vitals': torch.FloatTensor(vitals),
            'history': torch.FloatTensor(history),
            'symptoms': torch.FloatTensor(symptoms),
            'genetics': torch.FloatTensor(genetics),
            'demographics': torch.FloatTensor(demographics),
            'targets': torch.FloatTensor(targets)
        }
    
    def _get_source_names(self) -> List[str]:
        return ['lab_tests', 'imaging', 'vitals', 'history', 'symptoms', 'genetics', 'demographics']
    
    def _get_sample(self, idx: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        source_data = [
            self.data['lab_tests'][idx],
            self.data['imaging'][idx],
            self.data['vitals'][idx],
            self.data['history'][idx],
            self.data['symptoms'][idx],
            self.data['genetics'][idx],
            self.data['demographics'][idx]
        ]
        targets = self.data['targets'][idx]
        return source_data, targets


class EnvironmentalMonitoringDataset(MultiSourceDataset):
    """
    Environmental Monitoring Multi-Sensor Dataset
    
    Simulates distributed environmental sensor network:
    - Temperature sensors
    - Humidity sensors  
    - Air quality sensors
    - Wind sensors
    - Precipitation sensors
    - Solar radiation sensors
    - Soil sensors
    - Water quality sensors
    
    Target: Environmental condition prediction
    """
    
    def _load_data(self) -> Dict[str, Any]:
        n_samples = 12000 if self.split == 'train' else 2400
        np.random.seed(42 if self.split == 'train' else 123)
        
        # Temperature sensors (10 stations)
        temperature = np.random.randn(n_samples, 10) * 5.0 + 20.0
        
        # Humidity sensors (8 stations)
        humidity = np.random.randn(n_samples, 8) * 10.0 + 60.0
        
        # Air quality sensors (12 measurements)
        air_quality = np.random.randn(n_samples, 12) * 2.0
        
        # Wind sensors (6-dimensional: speed + direction)
        wind = np.random.randn(n_samples, 6) * 3.0
        
        # Precipitation sensors (5 stations)
        precipitation = np.random.randn(n_samples, 5) * 1.0
        
        # Solar radiation (4 measurements)
        solar = np.random.randn(n_samples, 4) * 2.0
        
        # Soil sensors (8 measurements)
        soil = np.random.randn(n_samples, 8) * 1.5
        
        # Water quality (6 measurements)
        water = np.random.randn(n_samples, 6) * 1.2
        
        # Target: Overall environmental index (4-dimensional)
        combined_signal = (temperature[:, :4] * 0.2 + 
                          humidity[:, :4] * 0.15 + 
                          air_quality[:, :4] * 0.25 + 
                          wind[:, :4] * 0.1 + 
                          precipitation[:, :4] * 0.1 + 
                          solar[:, :4] * 0.1 + 
                          soil[:, :4] * 0.05 + 
                          water[:, :4] * 0.05)
        targets = combined_signal + np.random.randn(n_samples, 4) * 0.3
        
        return {
            'temperature': torch.FloatTensor(temperature),
            'humidity': torch.FloatTensor(humidity),
            'air_quality': torch.FloatTensor(air_quality),
            'wind': torch.FloatTensor(wind),
            'precipitation': torch.FloatTensor(precipitation),
            'solar': torch.FloatTensor(solar),
            'soil': torch.FloatTensor(soil),
            'water': torch.FloatTensor(water),
            'targets': torch.FloatTensor(targets)
        }
    
    def _get_source_names(self) -> List[str]:
        return ['temperature', 'humidity', 'air_quality', 'wind', 'precipitation', 'solar', 'soil', 'water']
    
    def _get_sample(self, idx: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        source_data = [
            self.data['temperature'][idx],
            self.data['humidity'][idx],
            self.data['air_quality'][idx],
            self.data['wind'][idx],
            self.data['precipitation'][idx],
            self.data['solar'][idx],
            self.data['soil'][idx],
            self.data['water'][idx]
        ]
        targets = self.data['targets'][idx]
        return source_data, targets


class FinancialMarketsDataset(MultiSourceDataset):
    """
    Financial Markets Multi-Source Dataset
    
    Simulates multi-source financial data fusion:
    - Price data (OHLCV)
    - Technical indicators
    - Sentiment analysis
    - News features
    - Economic indicators
    - Social media signals
    
    Target: Price movement prediction
    """
    
    def _load_data(self) -> Dict[str, Any]:
        n_samples = 15000 if self.split == 'train' else 3000
        np.random.seed(42 if self.split == 'train' else 123)
        
        # Price data (20-dimensional OHLCV + derived)
        price_data = np.random.randn(n_samples, 20) * 2.0
        
        # Technical indicators (30-dimensional)
        technical = np.random.randn(n_samples, 30) * 1.5
        
        # Sentiment scores (10-dimensional)
        sentiment = np.random.randn(n_samples, 10) * 1.0
        
        # News features (50-dimensional NLP features)
        news = np.random.randn(n_samples, 50) * 1.2
        
        # Economic indicators (15-dimensional)
        economic = np.random.randn(n_samples, 15) * 0.8
        
        # Social media signals (25-dimensional)
        social = np.random.randn(n_samples, 25) * 1.3
        
        # Target: Price movement (3-dimensional: direction, magnitude, volatility)
        combined_signal = (price_data[:, :3] * 0.3 + 
                          technical[:, :3] * 0.25 + 
                          sentiment[:, :3] * 0.15 + 
                          news[:, :3] * 0.15 + 
                          economic[:, :3] * 0.1 + 
                          social[:, :3] * 0.05)
        targets = combined_signal + np.random.randn(n_samples, 3) * 0.2
        
        return {
            'price_data': torch.FloatTensor(price_data),
            'technical': torch.FloatTensor(technical),
            'sentiment': torch.FloatTensor(sentiment),
            'news': torch.FloatTensor(news),
            'economic': torch.FloatTensor(economic),
            'social': torch.FloatTensor(social),
            'targets': torch.FloatTensor(targets)
        }
    
    def _get_source_names(self) -> List[str]:
        return ['price_data', 'technical', 'sentiment', 'news', 'economic', 'social']
    
    def _get_sample(self, idx: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        source_data = [
            self.data['price_data'][idx],
            self.data['technical'][idx],
            self.data['sentiment'][idx],
            self.data['news'][idx],
            self.data['economic'][idx],
            self.data['social'][idx]
        ]
        targets = self.data['targets'][idx]
        return source_data, targets


class IoTNetworksDataset(MultiSourceDataset):
    """IoT Networks Multi-Sensor Dataset"""
    
    def _load_data(self) -> Dict[str, Any]:
        n_samples = 20000 if self.split == 'train' else 4000
        np.random.seed(42 if self.split == 'train' else 123)
        
        # Smart home sensors
        smart_home = np.random.randn(n_samples, 15) * 1.5
        
        # Industrial IoT sensors
        industrial = np.random.randn(n_samples, 25) * 2.0
        
        # Wearable devices
        wearables = np.random.randn(n_samples, 12) * 1.0
        
        # Vehicle sensors
        vehicles = np.random.randn(n_samples, 18) * 1.8
        
        # Infrastructure sensors
        infrastructure = np.random.randn(n_samples, 20) * 1.3
        
        # Target: System efficiency score
        combined_signal = (smart_home[:, :2] * 0.2 + 
                          industrial[:, :2] * 0.3 + 
                          wearables[:, :2] * 0.15 + 
                          vehicles[:, :2] * 0.2 + 
                          infrastructure[:, :2] * 0.15)
        targets = combined_signal + np.random.randn(n_samples, 2) * 0.2
        
        return {
            'smart_home': torch.FloatTensor(smart_home),
            'industrial': torch.FloatTensor(industrial),
            'wearables': torch.FloatTensor(wearables),
            'vehicles': torch.FloatTensor(vehicles),
            'infrastructure': torch.FloatTensor(infrastructure),
            'targets': torch.FloatTensor(targets)
        }
    
    def _get_source_names(self) -> List[str]:
        return ['smart_home', 'industrial', 'wearables', 'vehicles', 'infrastructure']
    
    def _get_sample(self, idx: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        source_data = [
            self.data['smart_home'][idx],
            self.data['industrial'][idx],
            self.data['wearables'][idx],
            self.data['vehicles'][idx],
            self.data['infrastructure'][idx]
        ]
        targets = self.data['targets'][idx]
        return source_data, targets


class WeatherForecastingDataset(MultiSourceDataset):
    """Weather Forecasting Multi-Station Dataset"""

    def _load_data(self) -> Dict[str, Any]:
        n_samples = 18000 if self.split == 'train' else 3600
        np.random.seed(42 if self.split == 'train' else 123)

        # Weather stations (different locations)
        station_1 = np.random.randn(n_samples, 12) * 2.0  # Temperature, pressure, humidity, wind
        station_2 = np.random.randn(n_samples, 12) * 2.0
        station_3 = np.random.randn(n_samples, 12) * 2.0

        # Satellite data (atmospheric conditions)
        satellite = np.random.randn(n_samples, 20) * 1.5

        # Radar data (precipitation)
        radar = np.random.randn(n_samples, 8) * 1.8

        # Target: Weather forecast (temperature, precipitation, wind)
        combined_signal = (station_1[:, :3] * 0.25 +
                          station_2[:, :3] * 0.25 +
                          station_3[:, :3] * 0.25 +
                          satellite[:, :3] * 0.15 +
                          radar[:, :3] * 0.1)
        targets = combined_signal + np.random.randn(n_samples, 3) * 0.3

        return {
            'station_1': torch.FloatTensor(station_1),
            'station_2': torch.FloatTensor(station_2),
            'station_3': torch.FloatTensor(station_3),
            'satellite': torch.FloatTensor(satellite),
            'radar': torch.FloatTensor(radar),
            'targets': torch.FloatTensor(targets)
        }

    def _get_source_names(self) -> List[str]:
        return ['station_1', 'station_2', 'station_3', 'satellite', 'radar']

    def _get_sample(self, idx: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        source_data = [
            self.data['station_1'][idx],
            self.data['station_2'][idx],
            self.data['station_3'][idx],
            self.data['satellite'][idx],
            self.data['radar'][idx]
        ]
        targets = self.data['targets'][idx]
        return source_data, targets


class TrafficManagementDataset(MultiSourceDataset):
    """Traffic Management Multi-Camera Dataset"""

    def _load_data(self) -> Dict[str, Any]:
        n_samples = 16000 if self.split == 'train' else 3200
        np.random.seed(42 if self.split == 'train' else 123)

        # Traffic cameras (visual features)
        camera_1 = np.random.randn(n_samples, 32) * 1.5
        camera_2 = np.random.randn(n_samples, 32) * 1.5

        # Loop detectors (vehicle counts, speeds)
        loop_detectors = np.random.randn(n_samples, 16) * 2.0

        # GPS probe data (floating car data)
        gps_probes = np.random.randn(n_samples, 24) * 1.8

        # Incident reports
        incidents = np.random.randn(n_samples, 8) * 1.0

        # Target: Traffic flow prediction (volume, speed, density)
        combined_signal = (camera_1[:, :3] * 0.2 +
                          camera_2[:, :3] * 0.2 +
                          loop_detectors[:, :3] * 0.3 +
                          gps_probes[:, :3] * 0.25 +
                          incidents[:, :3] * 0.05)
        targets = combined_signal + np.random.randn(n_samples, 3) * 0.25

        return {
            'camera_1': torch.FloatTensor(camera_1),
            'camera_2': torch.FloatTensor(camera_2),
            'loop_detectors': torch.FloatTensor(loop_detectors),
            'gps_probes': torch.FloatTensor(gps_probes),
            'incidents': torch.FloatTensor(incidents),
            'targets': torch.FloatTensor(targets)
        }

    def _get_source_names(self) -> List[str]:
        return ['camera_1', 'camera_2', 'loop_detectors', 'gps_probes', 'incidents']

    def _get_sample(self, idx: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        source_data = [
            self.data['camera_1'][idx],
            self.data['camera_2'][idx],
            self.data['loop_detectors'][idx],
            self.data['gps_probes'][idx],
            self.data['incidents'][idx]
        ]
        targets = self.data['targets'][idx]
        return source_data, targets


class EnergySystemsDataset(MultiSourceDataset):
    """Energy Systems Smart Grid Dataset"""

    def _load_data(self) -> Dict[str, Any]:
        n_samples = 14000 if self.split == 'train' else 2800
        np.random.seed(42 if self.split == 'train' else 123)

        # Power generation data
        generation = np.random.randn(n_samples, 20) * 2.0

        # Consumption patterns
        consumption = np.random.randn(n_samples, 18) * 1.8

        # Energy storage systems
        storage = np.random.randn(n_samples, 10) * 1.5

        # Weather conditions (affecting renewable generation)
        weather = np.random.randn(n_samples, 12) * 1.2

        # Grid status (voltage, frequency, load)
        grid_status = np.random.randn(n_samples, 15) * 1.3

        # Target: Energy demand prediction (peak, off-peak, renewable fraction)
        combined_signal = (generation[:, :3] * 0.25 +
                          consumption[:, :3] * 0.3 +
                          storage[:, :3] * 0.15 +
                          weather[:, :3] * 0.15 +
                          grid_status[:, :3] * 0.15)
        targets = combined_signal + np.random.randn(n_samples, 3) * 0.2

        return {
            'generation': torch.FloatTensor(generation),
            'consumption': torch.FloatTensor(consumption),
            'storage': torch.FloatTensor(storage),
            'weather': torch.FloatTensor(weather),
            'grid_status': torch.FloatTensor(grid_status),
            'targets': torch.FloatTensor(targets)
        }

    def _get_source_names(self) -> List[str]:
        return ['generation', 'consumption', 'storage', 'weather', 'grid_status']

    def _get_sample(self, idx: int) -> Tuple[List[torch.Tensor], torch.Tensor]:
        source_data = [
            self.data['generation'][idx],
            self.data['consumption'][idx],
            self.data['storage'][idx],
            self.data['weather'][idx],
            self.data['grid_status'][idx]
        ]
        targets = self.data['targets'][idx]
        return source_data, targets
