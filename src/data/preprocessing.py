"""
Data Preprocessing Utilities

Provides preprocessing and data augmentation utilities for multi-source datasets
including normalization, missing data handling, and reliability-aware augmentation.
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.impute import SimpleImputer, KNNImputer
import logging

logger = logging.getLogger(__name__)


class DataPreprocessor:
    """
    Multi-Source Data Preprocessor
    
    Handles preprocessing for multi-source data including:
    - Normalization/standardization
    - Missing data imputation
    - Outlier detection and handling
    - Data augmentation
    - Reliability-aware transformations
    """
    
    def __init__(
        self,
        normalization: str = 'standard',  # 'standard', 'minmax', 'robust', 'none'
        imputation: str = 'mean',  # 'mean', 'median', 'knn', 'none'
        outlier_handling: str = 'clip',  # 'clip', 'remove', 'none'
        augmentation: bool = True
    ):
        self.normalization = normalization
        self.imputation = imputation
        self.outlier_handling = outlier_handling
        self.augmentation = augmentation
        
        # Fitted preprocessors for each source
        self.scalers = {}
        self.imputers = {}
        self.outlier_bounds = {}
        
        # Statistics
        self.is_fitted = False
        self.source_stats = {}
        
    def fit(
        self,
        source_data: List[torch.Tensor],
        source_names: List[str]
    ):
        """
        Fit preprocessors on training data
        
        Args:
            source_data: List of [n_samples, feature_dim] tensors for each source
            source_names: Names of data sources
        """
        logger.info("Fitting data preprocessors...")
        
        self.source_names = source_names
        n_sources = len(source_data)
        
        for i, (data, name) in enumerate(zip(source_data, source_names)):
            logger.info(f"Fitting preprocessor for source {i+1}/{n_sources}: {name}")
            
            # Convert to numpy for sklearn compatibility
            data_np = data.numpy() if isinstance(data, torch.Tensor) else data
            
            # Fit imputer
            if self.imputation != 'none':
                imputer = self._create_imputer()
                imputer.fit(data_np)
                self.imputers[name] = imputer
                
                # Apply imputation for further preprocessing
                data_np = imputer.transform(data_np)
            
            # Fit scaler
            if self.normalization != 'none':
                scaler = self._create_scaler()
                scaler.fit(data_np)
                self.scalers[name] = scaler
                
                # Apply scaling for outlier detection
                data_np = scaler.transform(data_np)
            
            # Compute outlier bounds
            if self.outlier_handling != 'none':
                self.outlier_bounds[name] = self._compute_outlier_bounds(data_np)
            
            # Store statistics
            self.source_stats[name] = {
                'shape': data.shape,
                'mean': np.mean(data_np, axis=0),
                'std': np.std(data_np, axis=0),
                'min': np.min(data_np, axis=0),
                'max': np.max(data_np, axis=0),
                'missing_rate': np.mean(np.isnan(data.numpy())) if isinstance(data, torch.Tensor) else np.mean(np.isnan(data))
            }
        
        self.is_fitted = True
        logger.info("Data preprocessors fitted successfully")
    
    def transform(
        self,
        source_data: List[torch.Tensor],
        source_names: List[str],
        apply_augmentation: bool = False
    ) -> List[torch.Tensor]:
        """
        Transform data using fitted preprocessors
        
        Args:
            source_data: List of source data tensors
            source_names: Names of data sources
            apply_augmentation: Whether to apply data augmentation
            
        Returns:
            List of preprocessed data tensors
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor not fitted. Call fit() first.")
        
        transformed_data = []
        
        for data, name in zip(source_data, source_names):
            # Convert to numpy
            data_np = data.numpy() if isinstance(data, torch.Tensor) else data
            original_shape = data_np.shape
            
            # Handle batch dimension
            if len(original_shape) == 3:  # [batch_size, seq_len, features]
                batch_size, seq_len, n_features = original_shape
                data_np = data_np.reshape(-1, n_features)
            elif len(original_shape) == 2:  # [batch_size, features]
                batch_size, n_features = original_shape
                seq_len = 1
            else:
                raise ValueError(f"Unsupported data shape: {original_shape}")
            
            # Apply imputation
            if name in self.imputers:
                data_np = self.imputers[name].transform(data_np)
            
            # Apply scaling
            if name in self.scalers:
                data_np = self.scalers[name].transform(data_np)
            
            # Handle outliers
            if name in self.outlier_bounds:
                data_np = self._handle_outliers(data_np, self.outlier_bounds[name])
            
            # Apply augmentation
            if apply_augmentation and self.augmentation:
                data_np = self._apply_augmentation(data_np, name)
            
            # Reshape back to original format
            if len(original_shape) == 3:
                data_np = data_np.reshape(batch_size, seq_len, n_features)
            
            # Convert back to tensor
            transformed_tensor = torch.FloatTensor(data_np)
            transformed_data.append(transformed_tensor)
        
        return transformed_data
    
    def fit_transform(
        self,
        source_data: List[torch.Tensor],
        source_names: List[str],
        apply_augmentation: bool = False
    ) -> List[torch.Tensor]:
        """Fit preprocessors and transform data in one step"""
        self.fit(source_data, source_names)
        return self.transform(source_data, source_names, apply_augmentation)
    
    def _create_scaler(self):
        """Create scaler based on normalization type"""
        if self.normalization == 'standard':
            return StandardScaler()
        elif self.normalization == 'minmax':
            return MinMaxScaler()
        elif self.normalization == 'robust':
            return RobustScaler()
        else:
            raise ValueError(f"Unknown normalization: {self.normalization}")
    
    def _create_imputer(self):
        """Create imputer based on imputation type"""
        if self.imputation == 'mean':
            return SimpleImputer(strategy='mean')
        elif self.imputation == 'median':
            return SimpleImputer(strategy='median')
        elif self.imputation == 'knn':
            return KNNImputer(n_neighbors=5)
        else:
            raise ValueError(f"Unknown imputation: {self.imputation}")
    
    def _compute_outlier_bounds(self, data: np.ndarray) -> Dict[str, np.ndarray]:
        """Compute outlier bounds using IQR method"""
        q1 = np.percentile(data, 25, axis=0)
        q3 = np.percentile(data, 75, axis=0)
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        return {
            'lower': lower_bound,
            'upper': upper_bound
        }
    
    def _handle_outliers(self, data: np.ndarray, bounds: Dict[str, np.ndarray]) -> np.ndarray:
        """Handle outliers based on specified method"""
        if self.outlier_handling == 'clip':
            return np.clip(data, bounds['lower'], bounds['upper'])
        elif self.outlier_handling == 'remove':
            # Mark outliers as NaN (will be handled by imputation)
            outlier_mask = (data < bounds['lower']) | (data > bounds['upper'])
            data_clean = data.copy()
            data_clean[outlier_mask] = np.nan
            return data_clean
        else:
            return data
    
    def _apply_augmentation(self, data: np.ndarray, source_name: str) -> np.ndarray:
        """Apply data augmentation techniques"""
        augmented_data = data.copy()
        
        # Gaussian noise augmentation
        noise_std = 0.01 * np.std(data, axis=0)
        noise = np.random.normal(0, noise_std, data.shape)
        augmented_data += noise
        
        # Random scaling augmentation
        scale_factor = np.random.uniform(0.95, 1.05, data.shape[1])
        augmented_data *= scale_factor
        
        # Feature dropout (randomly set some features to mean)
        dropout_prob = 0.05
        dropout_mask = np.random.random(data.shape) < dropout_prob
        feature_means = np.mean(data, axis=0)
        augmented_data[dropout_mask] = feature_means[dropout_mask[0]]
        
        return augmented_data
    
    def inverse_transform(
        self,
        source_data: List[torch.Tensor],
        source_names: List[str]
    ) -> List[torch.Tensor]:
        """
        Inverse transform preprocessed data back to original scale
        
        Args:
            source_data: List of preprocessed data tensors
            source_names: Names of data sources
            
        Returns:
            List of data tensors in original scale
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor not fitted. Call fit() first.")
        
        inverse_data = []
        
        for data, name in zip(source_data, source_names):
            data_np = data.numpy() if isinstance(data, torch.Tensor) else data
            original_shape = data_np.shape
            
            # Handle batch dimension
            if len(original_shape) == 3:
                batch_size, seq_len, n_features = original_shape
                data_np = data_np.reshape(-1, n_features)
            
            # Inverse scaling
            if name in self.scalers:
                data_np = self.scalers[name].inverse_transform(data_np)
            
            # Reshape back
            if len(original_shape) == 3:
                data_np = data_np.reshape(batch_size, seq_len, n_features)
            
            inverse_tensor = torch.FloatTensor(data_np)
            inverse_data.append(inverse_tensor)
        
        return inverse_data
    
    def get_preprocessing_stats(self) -> Dict[str, Any]:
        """Get preprocessing statistics"""
        return {
            'is_fitted': self.is_fitted,
            'normalization': self.normalization,
            'imputation': self.imputation,
            'outlier_handling': self.outlier_handling,
            'augmentation': self.augmentation,
            'source_stats': self.source_stats,
            'n_sources': len(self.source_stats)
        }


class ReliabilityAwareAugmentation:
    """
    Reliability-Aware Data Augmentation
    
    Applies different augmentation strategies based on source reliability patterns
    to simulate realistic degradation scenarios.
    """
    
    def __init__(
        self,
        noise_schedule: str = 'inverse_reliability',  # How noise scales with reliability
        dropout_schedule: str = 'inverse_reliability',  # How dropout scales with reliability
        corruption_types: List[str] = ['gaussian', 'dropout', 'scaling', 'offset']
    ):
        self.noise_schedule = noise_schedule
        self.dropout_schedule = dropout_schedule
        self.corruption_types = corruption_types
    
    def augment_batch(
        self,
        source_data: List[torch.Tensor],
        reliability_weights: torch.Tensor,
        augmentation_strength: float = 1.0
    ) -> List[torch.Tensor]:
        """
        Apply reliability-aware augmentation to a batch
        
        Args:
            source_data: List of [batch_size, feature_dim] tensors
            reliability_weights: [n_sources] reliability weights
            augmentation_strength: Overall augmentation strength
            
        Returns:
            List of augmented data tensors
        """
        augmented_data = []
        
        for i, data in enumerate(source_data):
            reliability = reliability_weights[i].item()
            
            # Compute corruption parameters based on reliability
            noise_level = self._compute_noise_level(reliability, augmentation_strength)
            dropout_prob = self._compute_dropout_prob(reliability, augmentation_strength)
            
            # Apply corruptions
            corrupted_data = data.clone()
            
            if 'gaussian' in self.corruption_types:
                corrupted_data = self._add_gaussian_noise(corrupted_data, noise_level)
            
            if 'dropout' in self.corruption_types:
                corrupted_data = self._apply_feature_dropout(corrupted_data, dropout_prob)
            
            if 'scaling' in self.corruption_types:
                corrupted_data = self._apply_random_scaling(corrupted_data, reliability)
            
            if 'offset' in self.corruption_types:
                corrupted_data = self._apply_random_offset(corrupted_data, reliability)
            
            augmented_data.append(corrupted_data)
        
        return augmented_data
    
    def _compute_noise_level(self, reliability: float, strength: float) -> float:
        """Compute noise level based on reliability"""
        if self.noise_schedule == 'inverse_reliability':
            return strength * (1.0 - reliability) * 0.1
        elif self.noise_schedule == 'constant':
            return strength * 0.05
        else:
            return 0.0
    
    def _compute_dropout_prob(self, reliability: float, strength: float) -> float:
        """Compute dropout probability based on reliability"""
        if self.dropout_schedule == 'inverse_reliability':
            return strength * (1.0 - reliability) * 0.2
        elif self.dropout_schedule == 'constant':
            return strength * 0.1
        else:
            return 0.0
    
    def _add_gaussian_noise(self, data: torch.Tensor, noise_level: float) -> torch.Tensor:
        """Add Gaussian noise to data"""
        if noise_level <= 0:
            return data
        
        noise = torch.randn_like(data) * noise_level
        return data + noise
    
    def _apply_feature_dropout(self, data: torch.Tensor, dropout_prob: float) -> torch.Tensor:
        """Apply feature-wise dropout"""
        if dropout_prob <= 0:
            return data
        
        dropout_mask = torch.rand_like(data) > dropout_prob
        return data * dropout_mask.float()
    
    def _apply_random_scaling(self, data: torch.Tensor, reliability: float) -> torch.Tensor:
        """Apply random scaling based on reliability"""
        scale_variance = (1.0 - reliability) * 0.1
        scale_factors = torch.normal(1.0, scale_variance, size=(data.size(-1),))
        return data * scale_factors
    
    def _apply_random_offset(self, data: torch.Tensor, reliability: float) -> torch.Tensor:
        """Apply random offset based on reliability"""
        offset_std = (1.0 - reliability) * 0.05 * torch.std(data, dim=0)
        offset = torch.normal(0.0, offset_std, size=(data.size(-1),))
        return data + offset
