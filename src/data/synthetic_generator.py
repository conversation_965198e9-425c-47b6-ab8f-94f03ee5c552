"""
Synthetic Data Generator

Generates synthetic multi-source datasets for testing and validation
when real data is not available. Provides realistic correlation patterns
and reliability dynamics.
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from scipy.stats import multivariate_normal
from sklearn.datasets import make_regression, make_classification

logger = logging.getLogger(__name__)


class SyntheticDataGenerator:
    """
    Synthetic Multi-Source Data Generator
    
    Generates realistic multi-source datasets with:
    - Correlated source signals
    - Dynamic reliability patterns
    - Realistic noise characteristics
    - Ground truth for evaluation
    """
    
    def __init__(
        self,
        n_sources: int = 5,
        source_dims: Optional[List[int]] = None,
        output_dim: int = 2,
        correlation_strength: float = 0.7,
        noise_level: float = 0.1,
        reliability_dynamics: str = 'sinusoidal',  # 'static', 'sinusoidal', 'random_walk'
        seed: int = 42
    ):
        self.n_sources = n_sources
        self.source_dims = source_dims or [20 + i * 5 for i in range(n_sources)]
        self.output_dim = output_dim
        self.correlation_strength = correlation_strength
        self.noise_level = noise_level
        self.reliability_dynamics = reliability_dynamics
        self.seed = seed
        
        # Set random seeds
        np.random.seed(seed)
        torch.manual_seed(seed)
        
        # Generate correlation structure
        self.correlation_matrix = self._generate_correlation_matrix()
        
        # Generate source-to-output mappings
        self.source_weights = self._generate_source_weights()
        
        logger.info(f"Initialized synthetic data generator: "
                   f"{n_sources} sources, dims={self.source_dims}, "
                   f"output_dim={output_dim}")
    
    def generate_dataset(
        self,
        n_samples: int,
        sequence_length: int = 1,
        split_name: str = 'train'
    ) -> Dict[str, torch.Tensor]:
        """
        Generate a complete synthetic dataset
        
        Args:
            n_samples: Number of samples to generate
            sequence_length: Length of sequences (for temporal data)
            split_name: Name of the split (affects random seed)
            
        Returns:
            Dictionary containing source data, targets, and metadata
        """
        logger.info(f"Generating {split_name} dataset: {n_samples} samples")
        
        # Adjust seed for different splits
        split_seeds = {'train': 0, 'val': 1000, 'test': 2000, 'calibration': 3000}
        current_seed = self.seed + split_seeds.get(split_name, 0)
        np.random.seed(current_seed)
        torch.manual_seed(current_seed)
        
        # Generate base signals
        base_signals = self._generate_base_signals(n_samples, sequence_length)
        
        # Generate reliability patterns
        reliability_patterns = self._generate_reliability_patterns(n_samples, sequence_length)
        
        # Generate source data with reliability-dependent noise
        source_data = self._generate_source_data(base_signals, reliability_patterns)
        
        # Generate targets
        targets = self._generate_targets(base_signals, reliability_patterns)
        
        # Package results
        dataset = {
            'source_data': source_data,
            'targets': targets,
            'reliability_patterns': reliability_patterns,
            'base_signals': base_signals,
            'source_weights': self.source_weights,
            'correlation_matrix': self.correlation_matrix
        }
        
        return dataset
    
    def _generate_correlation_matrix(self) -> np.ndarray:
        """Generate correlation matrix between sources"""
        
        # Create block correlation structure
        correlation_matrix = np.eye(self.n_sources)
        
        for i in range(self.n_sources):
            for j in range(i + 1, self.n_sources):
                # Correlation decreases with distance
                distance = abs(i - j)
                correlation = self.correlation_strength * np.exp(-distance * 0.5)
                correlation_matrix[i, j] = correlation
                correlation_matrix[j, i] = correlation
        
        return correlation_matrix
    
    def _generate_source_weights(self) -> torch.Tensor:
        """Generate weights for combining sources to create targets"""
        
        # Generate random weights with some structure
        weights = torch.randn(self.n_sources, self.output_dim)
        
        # Normalize so that some sources are more important
        importance = torch.softmax(torch.randn(self.n_sources), dim=0)
        weights = weights * importance.unsqueeze(1)
        
        return weights
    
    def _generate_base_signals(
        self,
        n_samples: int,
        sequence_length: int
    ) -> torch.Tensor:
        """Generate correlated base signals for all sources"""
        
        # Generate correlated Gaussian signals
        total_samples = n_samples * sequence_length
        
        # Use multivariate normal with correlation structure
        mean = np.zeros(self.n_sources)
        cov = self.correlation_matrix
        
        base_signals = multivariate_normal.rvs(
            mean=mean, 
            cov=cov, 
            size=total_samples
        )
        
        # Reshape to [n_samples, sequence_length, n_sources]
        if sequence_length > 1:
            base_signals = base_signals.reshape(n_samples, sequence_length, self.n_sources)
        else:
            base_signals = base_signals.reshape(n_samples, self.n_sources)
        
        return torch.FloatTensor(base_signals)
    
    def _generate_reliability_patterns(
        self,
        n_samples: int,
        sequence_length: int
    ) -> torch.Tensor:
        """Generate dynamic reliability patterns for sources"""
        
        if self.reliability_dynamics == 'static':
            # Static reliability for each source
            base_reliability = torch.rand(self.n_sources) * 0.5 + 0.5  # [0.5, 1.0]
            reliability = base_reliability.unsqueeze(0).expand(n_samples, -1)
            
        elif self.reliability_dynamics == 'sinusoidal':
            # Sinusoidal reliability patterns
            time_steps = torch.arange(n_samples, dtype=torch.float32)
            reliability = torch.zeros(n_samples, self.n_sources)
            
            for i in range(self.n_sources):
                # Different frequency and phase for each source
                frequency = 0.01 + i * 0.005  # Different frequencies
                phase = i * np.pi / self.n_sources  # Different phases
                
                # Base reliability + sinusoidal variation
                base = 0.7
                amplitude = 0.2
                pattern = base + amplitude * torch.sin(2 * np.pi * frequency * time_steps + phase)
                reliability[:, i] = torch.clamp(pattern, 0.1, 1.0)
                
        elif self.reliability_dynamics == 'random_walk':
            # Random walk reliability patterns
            reliability = torch.zeros(n_samples, self.n_sources)
            
            for i in range(self.n_sources):
                # Initialize at random value
                current_reliability = torch.rand(1) * 0.5 + 0.5
                
                for t in range(n_samples):
                    # Random walk with mean reversion
                    drift = 0.01 * (0.7 - current_reliability)  # Mean reversion to 0.7
                    noise = torch.randn(1) * 0.02
                    current_reliability += drift + noise
                    current_reliability = torch.clamp(current_reliability, 0.1, 1.0)
                    reliability[t, i] = current_reliability
        
        else:
            # Random reliability
            reliability = torch.rand(n_samples, self.n_sources)
        
        return reliability
    
    def _generate_source_data(
        self,
        base_signals: torch.Tensor,
        reliability_patterns: torch.Tensor
    ) -> List[torch.Tensor]:
        """Generate source-specific data with reliability-dependent noise"""
        
        source_data = []
        
        for i in range(self.n_sources):
            source_dim = self.source_dims[i]
            
            if len(base_signals.shape) == 3:  # Sequence data
                n_samples, seq_len, _ = base_signals.shape
                
                # Create source-specific features
                source_features = torch.randn(n_samples, seq_len, source_dim)
                
                # Inject base signal into first few dimensions
                signal_dims = min(source_dim, self.output_dim)
                source_features[:, :, :signal_dims] = base_signals[:, :, i:i+1].expand(-1, -1, signal_dims)
                
                # Add reliability-dependent noise
                for t in range(seq_len):
                    noise_level = self.noise_level * (2.0 - reliability_patterns[:, i])
                    noise = torch.randn(n_samples, source_dim) * noise_level.unsqueeze(1)
                    source_features[:, t, :] += noise
                
            else:  # Non-sequence data
                n_samples, _ = base_signals.shape
                
                # Create source-specific features
                source_features = torch.randn(n_samples, source_dim)
                
                # Inject base signal
                signal_dims = min(source_dim, self.output_dim)
                source_features[:, :signal_dims] = base_signals[:, i:i+1].expand(-1, signal_dims)
                
                # Add reliability-dependent noise
                noise_level = self.noise_level * (2.0 - reliability_patterns[:, i])
                noise = torch.randn(n_samples, source_dim) * noise_level.unsqueeze(1)
                source_features += noise
            
            source_data.append(source_features)
        
        return source_data
    
    def _generate_targets(
        self,
        base_signals: torch.Tensor,
        reliability_patterns: torch.Tensor
    ) -> torch.Tensor:
        """Generate target values from base signals"""
        
        if len(base_signals.shape) == 3:  # Sequence data
            n_samples, seq_len, n_sources = base_signals.shape
            
            # Use weighted combination of base signals
            targets = torch.zeros(n_samples, seq_len, self.output_dim)
            
            for t in range(seq_len):
                # Weight by reliability
                weighted_signals = base_signals[:, t, :] * reliability_patterns
                targets[:, t, :] = torch.matmul(weighted_signals, self.source_weights)
            
            # Add small amount of target noise
            target_noise = torch.randn_like(targets) * 0.05
            targets += target_noise
            
        else:  # Non-sequence data
            # Weight by reliability
            weighted_signals = base_signals * reliability_patterns
            targets = torch.matmul(weighted_signals, self.source_weights)
            
            # Add target noise
            target_noise = torch.randn_like(targets) * 0.05
            targets += target_noise
        
        return targets
    
    def generate_evaluation_scenarios(
        self,
        n_samples: int = 1000
    ) -> Dict[str, Dict[str, torch.Tensor]]:
        """
        Generate specific evaluation scenarios for testing
        
        Returns:
            Dictionary of scenarios with different reliability patterns
        """
        scenarios = {}
        
        # Scenario 1: High reliability all sources
        high_reliability = torch.ones(n_samples, self.n_sources) * 0.9
        scenarios['high_reliability'] = self._generate_scenario_data(n_samples, high_reliability)
        
        # Scenario 2: Low reliability all sources
        low_reliability = torch.ones(n_samples, self.n_sources) * 0.3
        scenarios['low_reliability'] = self._generate_scenario_data(n_samples, low_reliability)
        
        # Scenario 3: Mixed reliability
        mixed_reliability = torch.rand(n_samples, self.n_sources)
        scenarios['mixed_reliability'] = self._generate_scenario_data(n_samples, mixed_reliability)
        
        # Scenario 4: Single dominant source
        dominant_reliability = torch.ones(n_samples, self.n_sources) * 0.2
        dominant_reliability[:, 0] = 0.95  # First source is highly reliable
        scenarios['dominant_source'] = self._generate_scenario_data(n_samples, dominant_reliability)
        
        # Scenario 5: Gradually degrading reliability
        degrading_reliability = torch.zeros(n_samples, self.n_sources)
        for i in range(self.n_sources):
            degrading_reliability[:, i] = torch.linspace(0.9, 0.2, n_samples)
        scenarios['degrading_reliability'] = self._generate_scenario_data(n_samples, degrading_reliability)
        
        return scenarios
    
    def _generate_scenario_data(
        self,
        n_samples: int,
        reliability_patterns: torch.Tensor
    ) -> Dict[str, torch.Tensor]:
        """Generate data for a specific reliability scenario"""
        
        base_signals = self._generate_base_signals(n_samples, 1)
        source_data = self._generate_source_data(base_signals, reliability_patterns)
        targets = self._generate_targets(base_signals, reliability_patterns)
        
        return {
            'source_data': source_data,
            'targets': targets,
            'reliability_patterns': reliability_patterns,
            'base_signals': base_signals
        }
    
    def get_ground_truth_weights(self) -> torch.Tensor:
        """Get ground truth source importance weights"""
        return self.source_weights
    
    def get_correlation_matrix(self) -> np.ndarray:
        """Get source correlation matrix"""
        return self.correlation_matrix
