"""
Evaluation and Metrics System

Comprehensive evaluation framework for multi-source data fusion methods
including all metrics mentioned in the paper:

Performance Metrics:
- RMSE (Root Mean Square Error)
- MAE (Mean Absolute Error)
- R² (Coefficient of Determination)

Uncertainty Metrics:
- ECE (Expected Calibration Error)
- MCE (Maximum Calibration Error)
- Reliability Diagrams
- Coverage Probability
- Interval Width

Computational Metrics:
- Training Time
- Inference Time
- Memory Usage
- FLOPs Count
- Convergence Rate

Trust Learning Metrics:
- Trust Weight Correlation
- Trust Convergence Rate
- Trust Stability
"""

from .metrics import (
    RegressionMetrics,
    UncertaintyMetrics,
    ComputationalMetrics,
    TrustMetrics,
    MetricsCalculator
)

from .evaluator import (
    ComprehensiveEvaluator,
    AblationStudyEvaluator,
    RobustnessEvaluator
)

from .visualization import (
    ResultsVisualizer,
    CalibrationPlotter,
    TrustEvolutionPlotter
)

from .statistical_tests import (
    StatisticalTester,
    SignificanceAnalyzer
)

__all__ = [
    # Metrics
    'RegressionMetrics',
    'UncertaintyMetrics', 
    'ComputationalMetrics',
    'TrustMetrics',
    'MetricsCalculator',
    
    # Evaluators
    'ComprehensiveEvaluator',
    'AblationStudyEvaluator',
    'RobustnessEvaluator',
    
    # Visualization
    'ResultsVisualizer',
    'CalibrationPlotter',
    'TrustEvolutionPlotter',
    
    # Statistical analysis
    'StatisticalTester',
    'SignificanceAnalyzer'
]
