"""
Comprehensive Evaluation Framework

Implements comprehensive evaluation including ablation studies and robustness testing
as described in the paper's experimental section.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Callable
import logging
import json
import os
from tqdm import tqdm
import copy

from .metrics import MetricsCalculator
from ..framework import TrustCalibratedFramework

logger = logging.getLogger(__name__)


class ComprehensiveEvaluator:
    """
    Comprehensive Evaluation System
    
    Provides unified evaluation across all datasets and metrics
    with support for statistical significance testing.
    """
    
    def __init__(
        self,
        metrics_calculator: Optional[MetricsCalculator] = None,
        device: str = 'auto'
    ):
        self.metrics_calculator = metrics_calculator or MetricsCalculator()
        
        if device == 'auto':
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        else:
            self.device = device
        
        self.results = {}
        
    def evaluate_framework(
        self,
        framework: TrustCalibratedFramework,
        test_loaders: Dict[str, torch.utils.data.DataLoader],
        dataset_names: List[str],
        n_seeds: int = 5,
        save_results: bool = True,
        results_dir: str = 'experiment_results'
    ) -> Dict[str, Dict[str, Any]]:
        """
        Comprehensive evaluation across multiple datasets and seeds
        
        Args:
            framework: Trust-calibrated framework to evaluate
            test_loaders: Dictionary of test data loaders
            dataset_names: List of dataset names to evaluate
            n_seeds: Number of random seeds for statistical robustness
            save_results: Whether to save results to disk
            results_dir: Directory to save results
            
        Returns:
            Dictionary with evaluation results for each dataset
        """
        logger.info(f"Starting comprehensive evaluation on {len(dataset_names)} datasets")
        
        if save_results:
            os.makedirs(results_dir, exist_ok=True)
        
        all_results = {}
        
        for dataset_name in dataset_names:
            logger.info(f"Evaluating on {dataset_name}")
            
            if dataset_name not in test_loaders:
                logger.warning(f"No test loader for {dataset_name}, skipping")
                continue
            
            dataset_results = self._evaluate_single_dataset(
                framework, test_loaders[dataset_name], dataset_name, n_seeds
            )
            
            all_results[dataset_name] = dataset_results
            
            # Save intermediate results
            if save_results:
                result_file = os.path.join(results_dir, f'{dataset_name}_results.json')
                with open(result_file, 'w') as f:
                    json.dump(dataset_results, f, indent=2, default=str)
        
        # Compute aggregate statistics
        aggregate_results = self._compute_aggregate_statistics(all_results)
        all_results['aggregate'] = aggregate_results
        
        # Save complete results
        if save_results:
            complete_file = os.path.join(results_dir, 'comprehensive_results.json')
            with open(complete_file, 'w') as f:
                json.dump(all_results, f, indent=2, default=str)
        
        self.results = all_results
        return all_results
    
    def _evaluate_single_dataset(
        self,
        framework: TrustCalibratedFramework,
        test_loader: torch.utils.data.DataLoader,
        dataset_name: str,
        n_seeds: int
    ) -> Dict[str, Any]:
        """Evaluate framework on a single dataset with multiple seeds"""
        
        seed_results = []
        
        for seed in range(n_seeds):
            logger.info(f"  Seed {seed + 1}/{n_seeds}")
            
            # Set random seeds
            torch.manual_seed(seed)
            np.random.seed(seed)
            
            # Reset framework state
            framework.reset_trust_learning()
            
            # Evaluate with current seed
            seed_result = self._evaluate_single_seed(framework, test_loader, dataset_name)
            seed_results.append(seed_result)
        
        # Aggregate results across seeds
        aggregated_results = self._aggregate_seed_results(seed_results)
        
        return {
            'dataset_name': dataset_name,
            'n_seeds': n_seeds,
            'seed_results': seed_results,
            'aggregated_results': aggregated_results
        }
    
    def _evaluate_single_seed(
        self,
        framework: TrustCalibratedFramework,
        test_loader: torch.utils.data.DataLoader,
        dataset_name: str
    ) -> Dict[str, Any]:
        """Evaluate framework for a single seed"""
        
        framework.model.eval()
        framework.trust_learner.eval()
        framework.uncertainty_module.eval()
        
        all_predictions = []
        all_targets = []
        all_uncertainties = []
        all_intervals = []
        all_trust_weights = []
        all_ground_truth_reliability = []
        
        inference_times = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="Evaluating", leave=False)):
                source_data = [data.to(self.device) for data in batch['source_data']]
                targets = batch['targets'].to(self.device)
                ground_truth_reliability = batch.get('reliability', None)
                
                # Measure inference time
                start_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
                end_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
                
                if start_time:
                    start_time.record()
                else:
                    start_cpu_time = time.time()
                
                # Get predictions with uncertainty
                output = framework.predict(
                    source_data,
                    return_uncertainty=True,
                    return_intervals=True
                )
                
                if end_time:
                    end_time.record()
                    torch.cuda.synchronize()
                    inference_time = start_time.elapsed_time(end_time) / 1000.0  # Convert to seconds
                else:
                    inference_time = time.time() - start_cpu_time
                
                inference_times.append(inference_time)
                
                # Collect results
                all_predictions.append(output['prediction'])
                all_targets.append(targets)
                all_uncertainties.append(output['total_uncertainty'])
                all_intervals.append((output['lower_bound'], output['upper_bound']))
                all_trust_weights.append(output['trust_weights'])
                
                if ground_truth_reliability is not None:
                    all_ground_truth_reliability.append(ground_truth_reliability)
        
        # Concatenate all results
        predictions = torch.cat(all_predictions, dim=0)
        targets = torch.cat(all_targets, dim=0)
        uncertainties = torch.cat(all_uncertainties, dim=0)
        lower_bounds = torch.cat([interval[0] for interval in all_intervals], dim=0)
        upper_bounds = torch.cat([interval[1] for interval in all_intervals], dim=0)
        
        # Trust weights (use last batch as representative)
        trust_weights = all_trust_weights[-1] if all_trust_weights else None
        ground_truth_reliability = torch.cat(all_ground_truth_reliability, dim=0) if all_ground_truth_reliability else None
        
        # Compute comprehensive metrics
        metrics = self.metrics_calculator.compute_all_metrics(
            predictions=predictions,
            targets=targets,
            uncertainties=uncertainties,
            lower_bounds=lower_bounds,
            upper_bounds=upper_bounds,
            trust_weights=trust_weights,
            ground_truth_reliability=ground_truth_reliability.mean(dim=0) if ground_truth_reliability is not None else None,
            model=framework.model,
            data_loader=test_loader
        )
        
        # Add timing metrics
        metrics['timing'] = {
            'mean_inference_time': np.mean(inference_times),
            'std_inference_time': np.std(inference_times),
            'total_inference_time': np.sum(inference_times)
        }
        
        return metrics
    
    def _aggregate_seed_results(self, seed_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate results across multiple seeds"""
        
        aggregated = {}
        
        # Get all metric categories
        metric_categories = seed_results[0].keys()
        
        for category in metric_categories:
            aggregated[category] = {}
            
            # Get all metrics in this category
            if isinstance(seed_results[0][category], dict):
                metric_names = seed_results[0][category].keys()
                
                for metric_name in metric_names:
                    # Collect values across seeds
                    values = []
                    for seed_result in seed_results:
                        if metric_name in seed_result[category]:
                            value = seed_result[category][metric_name]
                            if isinstance(value, (int, float)):
                                values.append(value)
                    
                    if values:
                        aggregated[category][metric_name] = {
                            'mean': np.mean(values),
                            'std': np.std(values),
                            'min': np.min(values),
                            'max': np.max(values),
                            'values': values
                        }
        
        return aggregated
    
    def _compute_aggregate_statistics(self, all_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Compute aggregate statistics across all datasets"""
        
        aggregate_stats = {}
        
        # Collect metrics across datasets
        dataset_names = [name for name in all_results.keys() if name != 'aggregate']
        
        if not dataset_names:
            return aggregate_stats
        
        # Get metric structure from first dataset
        first_dataset = all_results[dataset_names[0]]
        aggregated_results = first_dataset['aggregated_results']
        
        for category in aggregated_results.keys():
            aggregate_stats[category] = {}
            
            for metric_name in aggregated_results[category].keys():
                # Collect mean values across datasets
                dataset_means = []
                for dataset_name in dataset_names:
                    if (category in all_results[dataset_name]['aggregated_results'] and
                        metric_name in all_results[dataset_name]['aggregated_results'][category]):
                        mean_value = all_results[dataset_name]['aggregated_results'][category][metric_name]['mean']
                        dataset_means.append(mean_value)
                
                if dataset_means:
                    aggregate_stats[category][metric_name] = {
                        'overall_mean': np.mean(dataset_means),
                        'overall_std': np.std(dataset_means),
                        'dataset_means': dataset_means,
                        'n_datasets': len(dataset_means)
                    }
        
        return aggregate_stats
    
    def generate_summary_report(self) -> str:
        """Generate a summary report of evaluation results"""
        
        if not self.results:
            return "No evaluation results available."
        
        report = ["=" * 80]
        report.append("COMPREHENSIVE EVALUATION SUMMARY")
        report.append("=" * 80)
        
        # Overall statistics
        if 'aggregate' in self.results:
            report.append("\nOVERALL PERFORMANCE ACROSS DATASETS:")
            report.append("-" * 40)
            
            aggregate = self.results['aggregate']
            
            # Regression metrics
            if 'regression' in aggregate:
                report.append("\nRegression Metrics:")
                for metric, stats in aggregate['regression'].items():
                    report.append(f"  {metric.upper()}: {stats['overall_mean']:.4f} ± {stats['overall_std']:.4f}")
            
            # Uncertainty metrics
            if 'uncertainty' in aggregate:
                report.append("\nUncertainty Metrics:")
                for metric, stats in aggregate['uncertainty'].items():
                    report.append(f"  {metric.upper()}: {stats['overall_mean']:.4f} ± {stats['overall_std']:.4f}")
            
            # Trust metrics
            if 'trust' in aggregate:
                report.append("\nTrust Learning Metrics:")
                for metric, stats in aggregate['trust'].items():
                    report.append(f"  {metric}: {stats['overall_mean']:.4f} ± {stats['overall_std']:.4f}")
        
        # Per-dataset results
        report.append("\n\nPER-DATASET RESULTS:")
        report.append("-" * 40)
        
        for dataset_name, dataset_results in self.results.items():
            if dataset_name == 'aggregate':
                continue
            
            report.append(f"\n{dataset_name.upper()}:")
            
            aggregated = dataset_results['aggregated_results']
            
            if 'regression' in aggregated:
                rmse = aggregated['regression'].get('rmse', {}).get('mean', 'N/A')
                mae = aggregated['regression'].get('mae', {}).get('mean', 'N/A')
                report.append(f"  RMSE: {rmse:.4f}" if isinstance(rmse, float) else f"  RMSE: {rmse}")
                report.append(f"  MAE: {mae:.4f}" if isinstance(mae, float) else f"  MAE: {mae}")
            
            if 'uncertainty' in aggregated:
                ece = aggregated['uncertainty'].get('ece', {}).get('mean', 'N/A')
                coverage = aggregated['uncertainty'].get('coverage', {}).get('mean', 'N/A')
                report.append(f"  ECE: {ece:.4f}" if isinstance(ece, float) else f"  ECE: {ece}")
                report.append(f"  Coverage: {coverage:.4f}" if isinstance(coverage, float) else f"  Coverage: {coverage}")
        
        return "\n".join(report)


class AblationStudyEvaluator:
    """
    Ablation Study Evaluator
    
    Systematically evaluates the contribution of each framework component
    by removing them one at a time.
    """
    
    def __init__(self, base_framework: TrustCalibratedFramework):
        self.base_framework = base_framework
        self.ablation_results = {}
    
    def run_ablation_study(
        self,
        test_loader: torch.utils.data.DataLoader,
        components_to_ablate: List[str] = ['trust_learning', 'attention_modulation', 'uncertainty_decomposition', 'conformal_prediction']
    ) -> Dict[str, Dict[str, float]]:
        """
        Run comprehensive ablation study
        
        Args:
            test_loader: Test data loader
            components_to_ablate: List of components to ablate
            
        Returns:
            Dictionary with ablation results for each component
        """
        logger.info("Starting ablation study")
        
        # Evaluate full model
        full_model_results = self._evaluate_configuration(test_loader, {})
        self.ablation_results['full_model'] = full_model_results
        
        # Evaluate each ablation
        for component in components_to_ablate:
            logger.info(f"Ablating {component}")
            
            ablation_config = {component: False}
            ablated_results = self._evaluate_configuration(test_loader, ablation_config)
            self.ablation_results[f'without_{component}'] = ablated_results
        
        # Compute contribution of each component
        contributions = self._compute_component_contributions()
        self.ablation_results['contributions'] = contributions
        
        return self.ablation_results
    
    def _evaluate_configuration(
        self,
        test_loader: torch.utils.data.DataLoader,
        ablation_config: Dict[str, bool]
    ) -> Dict[str, float]:
        """Evaluate a specific configuration"""
        
        # Create modified framework based on ablation config
        modified_framework = self._create_ablated_framework(ablation_config)
        
        # Evaluate
        evaluator = ComprehensiveEvaluator()
        results = evaluator._evaluate_single_seed(modified_framework, test_loader, 'ablation')
        
        # Extract key metrics
        key_metrics = {}
        if 'regression' in results:
            key_metrics.update(results['regression'])
        if 'uncertainty' in results:
            key_metrics.update(results['uncertainty'])
        if 'trust' in results:
            key_metrics.update(results['trust'])
        
        return key_metrics
    
    def _create_ablated_framework(self, ablation_config: Dict[str, bool]):
        """Create framework with specified components ablated"""
        
        # For this implementation, we'll create a wrapper that modifies behavior
        # In practice, you might need to create actual ablated versions
        
        class AblatedFramework:
            def __init__(self, base_framework, ablation_config):
                self.base_framework = base_framework
                self.ablation_config = ablation_config
                
                # Copy attributes
                self.model = base_framework.model
                self.trust_learner = base_framework.trust_learner
                self.uncertainty_module = base_framework.uncertainty_module
                self.conformal_predictor = base_framework.conformal_predictor
            
            def predict(self, source_data, **kwargs):
                # Modify prediction based on ablation config
                if self.ablation_config.get('trust_learning', True):
                    # Use learned trust weights
                    trust_weights = self.trust_learner.get_trust_weights()
                else:
                    # Use uniform trust weights
                    n_sources = len(source_data)
                    trust_weights = torch.ones(n_sources) / n_sources
                
                # Get base prediction
                output = self.base_framework.predict(source_data, **kwargs)
                
                if not self.ablation_config.get('uncertainty_decomposition', True):
                    # Use simple uncertainty estimate
                    output['total_uncertainty'] = output.get('aleatoric_uncertainty', 
                                                           torch.ones_like(output['prediction']) * 0.1)
                
                if not self.ablation_config.get('conformal_prediction', True):
                    # Use simple Gaussian intervals
                    uncertainty = output['total_uncertainty']
                    output['lower_bound'] = output['prediction'] - 1.96 * torch.sqrt(uncertainty)
                    output['upper_bound'] = output['prediction'] + 1.96 * torch.sqrt(uncertainty)
                
                return output
            
            def reset_trust_learning(self):
                self.base_framework.reset_trust_learning()
        
        return AblatedFramework(self.base_framework, ablation_config)
    
    def _compute_component_contributions(self) -> Dict[str, Dict[str, float]]:
        """Compute the contribution of each component"""
        
        contributions = {}
        full_model_metrics = self.ablation_results['full_model']
        
        for ablation_name, ablated_metrics in self.ablation_results.items():
            if ablation_name.startswith('without_'):
                component_name = ablation_name.replace('without_', '')
                contributions[component_name] = {}
                
                for metric_name, full_value in full_model_metrics.items():
                    if metric_name in ablated_metrics:
                        ablated_value = ablated_metrics[metric_name]
                        
                        # Compute improvement (positive means component helps)
                        if metric_name in ['rmse', 'mae', 'ece']:  # Lower is better
                            improvement = (ablated_value - full_value) / ablated_value * 100
                        else:  # Higher is better
                            improvement = (full_value - ablated_value) / ablated_value * 100
                        
                        contributions[component_name][metric_name] = improvement
        
        return contributions


class RobustnessEvaluator:
    """
    Robustness Evaluator
    
    Tests framework robustness under various challenging conditions
    including noise, missing data, and adversarial scenarios.
    """
    
    def __init__(self, framework: TrustCalibratedFramework):
        self.framework = framework
        self.robustness_results = {}
    
    def evaluate_noise_robustness(
        self,
        test_loader: torch.utils.data.DataLoader,
        noise_levels: List[float] = [0.1, 0.2, 0.5, 1.0]
    ) -> Dict[str, Dict[str, float]]:
        """Evaluate robustness to different noise levels"""
        
        logger.info("Evaluating noise robustness")
        noise_results = {}
        
        for noise_level in noise_levels:
            logger.info(f"  Testing noise level: {noise_level}")
            
            # Create noisy test loader
            noisy_results = self._evaluate_with_noise(test_loader, noise_level)
            noise_results[f'noise_{noise_level}'] = noisy_results
        
        self.robustness_results['noise_robustness'] = noise_results
        return noise_results
    
    def evaluate_missing_data_robustness(
        self,
        test_loader: torch.utils.data.DataLoader,
        missing_rates: List[float] = [0.1, 0.2, 0.3, 0.5]
    ) -> Dict[str, Dict[str, float]]:
        """Evaluate robustness to missing data"""
        
        logger.info("Evaluating missing data robustness")
        missing_results = {}
        
        for missing_rate in missing_rates:
            logger.info(f"  Testing missing rate: {missing_rate}")
            
            missing_results_rate = self._evaluate_with_missing_data(test_loader, missing_rate)
            missing_results[f'missing_{missing_rate}'] = missing_results_rate
        
        self.robustness_results['missing_data_robustness'] = missing_results
        return missing_results
    
    def _evaluate_with_noise(
        self,
        test_loader: torch.utils.data.DataLoader,
        noise_level: float
    ) -> Dict[str, float]:
        """Evaluate with added noise"""
        
        self.framework.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                source_data = [data.to(self.framework.device) for data in batch['source_data']]
                targets = batch['targets'].to(self.framework.device)
                
                # Add noise to source data
                noisy_source_data = []
                for data in source_data:
                    noise = torch.randn_like(data) * noise_level
                    noisy_data = data + noise
                    noisy_source_data.append(noisy_data)
                
                # Get predictions
                output = self.framework.predict(noisy_source_data)
                
                all_predictions.append(output['prediction'])
                all_targets.append(targets)
        
        # Compute metrics
        predictions = torch.cat(all_predictions, dim=0)
        targets = torch.cat(all_targets, dim=0)
        
        metrics_calc = MetricsCalculator()
        metrics = metrics_calc.regression_metrics.compute_all_regression_metrics(predictions, targets)
        
        return metrics
    
    def _evaluate_with_missing_data(
        self,
        test_loader: torch.utils.data.DataLoader,
        missing_rate: float
    ) -> Dict[str, float]:
        """Evaluate with missing data"""
        
        self.framework.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                source_data = [data.to(self.framework.device) for data in batch['source_data']]
                targets = batch['targets'].to(self.framework.device)
                
                # Randomly mask data
                masked_source_data = []
                for data in source_data:
                    mask = torch.rand_like(data) > missing_rate
                    masked_data = data * mask.float()
                    masked_source_data.append(masked_data)
                
                # Get predictions
                output = self.framework.predict(masked_source_data)
                
                all_predictions.append(output['prediction'])
                all_targets.append(targets)
        
        # Compute metrics
        predictions = torch.cat(all_predictions, dim=0)
        targets = torch.cat(all_targets, dim=0)
        
        metrics_calc = MetricsCalculator()
        metrics = metrics_calc.regression_metrics.compute_all_regression_metrics(predictions, targets)
        
        return metrics
