"""
Comprehensive Metrics Implementation

Implements all evaluation metrics mentioned in the paper for thorough
performance assessment of multi-source data fusion methods.
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
import time
import psutil
import logging
from scipy import stats
from sklearn.metrics import r2_score
import matplotlib.pyplot as plt

logger = logging.getLogger(__name__)


class RegressionMetrics:
    """
    Regression Performance Metrics
    
    Implements standard regression metrics for evaluating prediction accuracy.
    """
    
    @staticmethod
    def rmse(predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """Root Mean Square Error"""
        mse = torch.mean((predictions - targets) ** 2)
        return torch.sqrt(mse).item()
    
    @staticmethod
    def mae(predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """Mean Absolute Error"""
        return torch.mean(torch.abs(predictions - targets)).item()
    
    @staticmethod
    def mape(predictions: torch.Tensor, targets: torch.Tensor, epsilon: float = 1e-8) -> float:
        """Mean Absolute Percentage Error"""
        return torch.mean(torch.abs((targets - predictions) / (targets + epsilon))).item() * 100
    
    @staticmethod
    def r2_score_torch(predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """R-squared (Coefficient of Determination)"""
        ss_res = torch.sum((targets - predictions) ** 2)
        ss_tot = torch.sum((targets - torch.mean(targets)) ** 2)
        return (1 - ss_res / ss_tot).item()
    
    @staticmethod
    def explained_variance(predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """Explained Variance Score"""
        var_y = torch.var(targets)
        var_residual = torch.var(targets - predictions)
        return (1 - var_residual / var_y).item()
    
    @staticmethod
    def compute_all_regression_metrics(
        predictions: torch.Tensor, 
        targets: torch.Tensor
    ) -> Dict[str, float]:
        """Compute all regression metrics"""
        return {
            'rmse': RegressionMetrics.rmse(predictions, targets),
            'mae': RegressionMetrics.mae(predictions, targets),
            'mape': RegressionMetrics.mape(predictions, targets),
            'r2_score': RegressionMetrics.r2_score_torch(predictions, targets),
            'explained_variance': RegressionMetrics.explained_variance(predictions, targets)
        }


class UncertaintyMetrics:
    """
    Uncertainty Quantification Metrics
    
    Implements metrics for evaluating uncertainty calibration and quality.
    """
    
    @staticmethod
    def expected_calibration_error(
        predictions: torch.Tensor,
        targets: torch.Tensor,
        uncertainties: torch.Tensor,
        n_bins: int = 10
    ) -> float:
        """
        Expected Calibration Error (ECE)
        
        Measures the difference between predicted confidence and actual accuracy
        across different confidence bins.
        """
        # Flatten tensors
        predictions_flat = predictions.flatten()
        targets_flat = targets.flatten()
        uncertainties_flat = uncertainties.flatten()
        
        # Compute prediction errors
        errors = torch.abs(predictions_flat - targets_flat)
        
        # Create bins based on uncertainty quantiles
        bin_boundaries = torch.quantile(uncertainties_flat, 
                                       torch.linspace(0, 1, n_bins + 1))
        
        ece = 0.0
        total_samples = len(predictions_flat)
        
        for i in range(n_bins):
            bin_lower = bin_boundaries[i]
            bin_upper = bin_boundaries[i + 1]
            
            # Find samples in this bin
            in_bin = (uncertainties_flat > bin_lower) & (uncertainties_flat <= bin_upper)
            
            if torch.sum(in_bin) > 0:
                # Average error and uncertainty in this bin
                avg_error = torch.mean(errors[in_bin])
                avg_uncertainty = torch.mean(uncertainties_flat[in_bin])
                
                # Bin weight (proportion of samples in bin)
                bin_weight = torch.sum(in_bin).float() / total_samples
                
                # Add to ECE
                ece += bin_weight * torch.abs(avg_uncertainty - avg_error)
        
        return ece.item()
    
    @staticmethod
    def maximum_calibration_error(
        predictions: torch.Tensor,
        targets: torch.Tensor,
        uncertainties: torch.Tensor,
        n_bins: int = 10
    ) -> float:
        """Maximum Calibration Error (MCE)"""
        predictions_flat = predictions.flatten()
        targets_flat = targets.flatten()
        uncertainties_flat = uncertainties.flatten()
        
        errors = torch.abs(predictions_flat - targets_flat)
        bin_boundaries = torch.quantile(uncertainties_flat, 
                                       torch.linspace(0, 1, n_bins + 1))
        
        mce = 0.0
        
        for i in range(n_bins):
            bin_lower = bin_boundaries[i]
            bin_upper = bin_boundaries[i + 1]
            
            in_bin = (uncertainties_flat > bin_lower) & (uncertainties_flat <= bin_upper)
            
            if torch.sum(in_bin) > 0:
                avg_error = torch.mean(errors[in_bin])
                avg_uncertainty = torch.mean(uncertainties_flat[in_bin])
                
                calibration_error = torch.abs(avg_uncertainty - avg_error)
                mce = max(mce, calibration_error.item())
        
        return mce
    
    @staticmethod
    def coverage_probability(
        predictions: torch.Tensor,
        targets: torch.Tensor,
        lower_bounds: torch.Tensor,
        upper_bounds: torch.Tensor
    ) -> float:
        """
        Coverage Probability
        
        Fraction of targets that fall within prediction intervals.
        """
        in_interval = (targets >= lower_bounds) & (targets <= upper_bounds)
        coverage = torch.mean(in_interval.float())
        return coverage.item()
    
    @staticmethod
    def average_interval_width(
        lower_bounds: torch.Tensor,
        upper_bounds: torch.Tensor
    ) -> float:
        """Average width of prediction intervals"""
        interval_widths = upper_bounds - lower_bounds
        return torch.mean(interval_widths).item()
    
    @staticmethod
    def interval_score(
        predictions: torch.Tensor,
        targets: torch.Tensor,
        lower_bounds: torch.Tensor,
        upper_bounds: torch.Tensor,
        alpha: float = 0.1
    ) -> float:
        """
        Interval Score (Winkler Score)
        
        Proper scoring rule for prediction intervals that penalizes
        both width and miscoverage.
        """
        interval_width = upper_bounds - lower_bounds
        
        # Penalties for being outside the interval
        lower_penalty = 2 * alpha * torch.clamp(lower_bounds - targets, min=0)
        upper_penalty = 2 * alpha * torch.clamp(targets - upper_bounds, min=0)
        
        # Total score
        score = interval_width + lower_penalty + upper_penalty
        return torch.mean(score).item()
    
    @staticmethod
    def reliability_diagram_data(
        predictions: torch.Tensor,
        targets: torch.Tensor,
        uncertainties: torch.Tensor,
        n_bins: int = 10
    ) -> Dict[str, List[float]]:
        """
        Generate data for reliability diagram
        
        Returns:
            Dictionary with bin data for plotting reliability diagrams
        """
        predictions_flat = predictions.flatten()
        targets_flat = targets.flatten()
        uncertainties_flat = uncertainties.flatten()
        
        errors = torch.abs(predictions_flat - targets_flat)
        bin_boundaries = torch.quantile(uncertainties_flat, 
                                       torch.linspace(0, 1, n_bins + 1))
        
        bin_data = {
            'bin_centers': [],
            'avg_errors': [],
            'avg_uncertainties': [],
            'bin_counts': [],
            'calibration_errors': []
        }
        
        for i in range(n_bins):
            bin_lower = bin_boundaries[i]
            bin_upper = bin_boundaries[i + 1]
            
            in_bin = (uncertainties_flat > bin_lower) & (uncertainties_flat <= bin_upper)
            
            if torch.sum(in_bin) > 0:
                avg_error = torch.mean(errors[in_bin]).item()
                avg_uncertainty = torch.mean(uncertainties_flat[in_bin]).item()
                bin_count = torch.sum(in_bin).item()
                
                bin_data['bin_centers'].append((bin_lower + bin_upper).item() / 2)
                bin_data['avg_errors'].append(avg_error)
                bin_data['avg_uncertainties'].append(avg_uncertainty)
                bin_data['bin_counts'].append(bin_count)
                bin_data['calibration_errors'].append(abs(avg_uncertainty - avg_error))
        
        return bin_data
    
    @staticmethod
    def compute_all_uncertainty_metrics(
        predictions: torch.Tensor,
        targets: torch.Tensor,
        uncertainties: torch.Tensor,
        lower_bounds: Optional[torch.Tensor] = None,
        upper_bounds: Optional[torch.Tensor] = None,
        alpha: float = 0.1
    ) -> Dict[str, float]:
        """Compute all uncertainty metrics"""
        metrics = {
            'ece': UncertaintyMetrics.expected_calibration_error(
                predictions, targets, uncertainties
            ),
            'mce': UncertaintyMetrics.maximum_calibration_error(
                predictions, targets, uncertainties
            )
        }
        
        if lower_bounds is not None and upper_bounds is not None:
            metrics.update({
                'coverage': UncertaintyMetrics.coverage_probability(
                    predictions, targets, lower_bounds, upper_bounds
                ),
                'avg_interval_width': UncertaintyMetrics.average_interval_width(
                    lower_bounds, upper_bounds
                ),
                'interval_score': UncertaintyMetrics.interval_score(
                    predictions, targets, lower_bounds, upper_bounds, alpha
                )
            })
        
        return metrics


class ComputationalMetrics:
    """
    Computational Efficiency Metrics
    
    Measures computational performance including timing, memory, and FLOPs.
    """
    
    def __init__(self):
        self.start_time = None
        self.start_memory = None
        self.process = psutil.Process()
    
    def start_timing(self):
        """Start timing measurement"""
        self.start_time = time.time()
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
    
    def end_timing(self) -> Dict[str, float]:
        """End timing measurement and return metrics"""
        if self.start_time is None:
            raise ValueError("Must call start_timing() first")
        
        end_time = time.time()
        end_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            'elapsed_time': end_time - self.start_time,
            'memory_usage': end_memory - self.start_memory,
            'peak_memory': end_memory
        }
    
    @staticmethod
    def measure_inference_time(
        model,
        data_loader,
        device: str = 'cpu',
        n_runs: int = 10
    ) -> Dict[str, float]:
        """Measure inference time statistics"""
        model.eval()
        inference_times = []
        
        with torch.no_grad():
            for run in range(n_runs):
                batch = next(iter(data_loader))
                source_data = [data.to(device) for data in batch['source_data']]
                
                start_time = time.time()
                _ = model(source_data)
                end_time = time.time()
                
                inference_times.append(end_time - start_time)
        
        return {
            'mean_inference_time': np.mean(inference_times),
            'std_inference_time': np.std(inference_times),
            'min_inference_time': np.min(inference_times),
            'max_inference_time': np.max(inference_times)
        }
    
    @staticmethod
    def count_parameters(model) -> Dict[str, int]:
        """Count model parameters"""
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'non_trainable_parameters': total_params - trainable_params
        }
    
    @staticmethod
    def estimate_flops(
        model,
        input_shapes: List[Tuple[int, ...]],
        device: str = 'cpu'
    ) -> int:
        """
        Estimate FLOPs for model inference
        
        Note: This is a simplified estimation. For accurate FLOP counting,
        consider using specialized libraries like fvcore or ptflops.
        """
        model.eval()
        
        # Create dummy inputs
        dummy_inputs = [torch.randn(shape).to(device) for shape in input_shapes]
        
        # Simple FLOP estimation based on parameter count and operations
        total_params = sum(p.numel() for p in model.parameters())
        
        # Rough estimation: 2 FLOPs per parameter (multiply-add)
        estimated_flops = total_params * 2
        
        return estimated_flops


class TrustMetrics:
    """
    Trust Learning Metrics
    
    Evaluates the quality and dynamics of trust weight learning.
    """
    
    @staticmethod
    def trust_weight_correlation(
        predicted_trust: torch.Tensor,
        ground_truth_reliability: torch.Tensor
    ) -> float:
        """
        Correlation between predicted trust weights and ground truth reliability
        """
        # Flatten tensors
        predicted_flat = predicted_trust.flatten()
        ground_truth_flat = ground_truth_reliability.flatten()
        
        # Compute Pearson correlation
        correlation = torch.corrcoef(torch.stack([predicted_flat, ground_truth_flat]))[0, 1]
        return correlation.item()
    
    @staticmethod
    def trust_convergence_rate(
        trust_history: List[torch.Tensor],
        ground_truth_reliability: torch.Tensor,
        convergence_threshold: float = 0.95
    ) -> int:
        """
        Number of steps to reach convergence threshold correlation
        """
        for step, trust_weights in enumerate(trust_history):
            correlation = TrustMetrics.trust_weight_correlation(
                trust_weights, ground_truth_reliability
            )
            if correlation >= convergence_threshold:
                return step
        
        return len(trust_history)  # Did not converge
    
    @staticmethod
    def trust_stability(
        trust_history: List[torch.Tensor],
        window_size: int = 50
    ) -> float:
        """
        Measure stability of trust weights over time
        """
        if len(trust_history) < window_size:
            return 0.0
        
        # Compute variance in trust weights over sliding window
        recent_trust = torch.stack(trust_history[-window_size:])  # [window_size, n_sources]
        trust_variance = torch.var(recent_trust, dim=0)  # [n_sources]
        
        # Average variance across sources
        return torch.mean(trust_variance).item()
    
    @staticmethod
    def trust_ranking_accuracy(
        predicted_trust: torch.Tensor,
        ground_truth_reliability: torch.Tensor
    ) -> float:
        """
        Accuracy of trust-based source ranking
        """
        # Get rankings
        predicted_ranking = torch.argsort(predicted_trust, descending=True)
        ground_truth_ranking = torch.argsort(ground_truth_reliability, descending=True)
        
        # Compute ranking correlation (Spearman's rank correlation)
        n_sources = len(predicted_trust)
        rank_diff = 0
        
        for i in range(n_sources):
            pred_rank = torch.where(predicted_ranking == i)[0].item()
            true_rank = torch.where(ground_truth_ranking == i)[0].item()
            rank_diff += (pred_rank - true_rank) ** 2
        
        spearman_rho = 1 - (6 * rank_diff) / (n_sources * (n_sources ** 2 - 1))
        return spearman_rho


class MetricsCalculator:
    """
    Unified Metrics Calculator
    
    Provides a single interface for computing all evaluation metrics.
    """
    
    def __init__(self):
        self.regression_metrics = RegressionMetrics()
        self.uncertainty_metrics = UncertaintyMetrics()
        self.computational_metrics = ComputationalMetrics()
        self.trust_metrics = TrustMetrics()
    
    def compute_all_metrics(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor,
        uncertainties: Optional[torch.Tensor] = None,
        lower_bounds: Optional[torch.Tensor] = None,
        upper_bounds: Optional[torch.Tensor] = None,
        trust_weights: Optional[torch.Tensor] = None,
        ground_truth_reliability: Optional[torch.Tensor] = None,
        model: Optional[torch.nn.Module] = None,
        data_loader: Optional[torch.utils.data.DataLoader] = None
    ) -> Dict[str, Any]:
        """
        Compute comprehensive evaluation metrics
        
        Args:
            predictions: Model predictions
            targets: Ground truth targets
            uncertainties: Uncertainty estimates
            lower_bounds: Lower bounds of prediction intervals
            upper_bounds: Upper bounds of prediction intervals
            trust_weights: Learned trust weights
            ground_truth_reliability: Ground truth source reliability
            model: Model for computational metrics
            data_loader: Data loader for timing measurements
            
        Returns:
            Dictionary with all computed metrics
        """
        results = {}
        
        # Regression metrics
        results['regression'] = self.regression_metrics.compute_all_regression_metrics(
            predictions, targets
        )
        
        # Uncertainty metrics
        if uncertainties is not None:
            results['uncertainty'] = self.uncertainty_metrics.compute_all_uncertainty_metrics(
                predictions, targets, uncertainties, lower_bounds, upper_bounds
            )
        
        # Trust metrics
        if trust_weights is not None and ground_truth_reliability is not None:
            results['trust'] = {
                'trust_correlation': self.trust_metrics.trust_weight_correlation(
                    trust_weights, ground_truth_reliability
                ),
                'trust_ranking_accuracy': self.trust_metrics.trust_ranking_accuracy(
                    trust_weights, ground_truth_reliability
                )
            }
        
        # Computational metrics
        if model is not None:
            results['computational'] = self.computational_metrics.count_parameters(model)
            
            if data_loader is not None:
                timing_metrics = self.computational_metrics.measure_inference_time(
                    model, data_loader
                )
                results['computational'].update(timing_metrics)
        
        return results
