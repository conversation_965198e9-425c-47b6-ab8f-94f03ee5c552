"""
Experimental Framework

Comprehensive experimental system for reproducing all results from the paper
including main performance comparisons, ablation studies, and robustness analysis.
"""

from .experiment_runner import (
    ComprehensiveExperimentRunner,
    MainPerformanceExperiment,
    AblationStudyExperiment,
    RobustnessAnalysisExperiment
)

from .results_generator import (
    ResultsGenerator,
    TableGenerator,
    FigureGenerator
)

from .config_manager import (
    ExperimentConfig,
    ConfigManager
)

__all__ = [
    # Experiment runners
    'ComprehensiveExperimentRunner',
    'MainPerformanceExperiment',
    'AblationStudyExperiment', 
    'RobustnessAnalysisExperiment',
    
    # Results generation
    'ResultsGenerator',
    'TableGenerator',
    'FigureGenerator',
    
    # Configuration
    'ExperimentConfig',
    'ConfigManager'
]
