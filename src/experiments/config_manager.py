"""
Experiment Configuration Management

Manages configuration for comprehensive experiments including
hyperparameters, dataset settings, and experimental protocols.
"""

import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)


@dataclass
class ExperimentConfig:
    """
    Comprehensive Experiment Configuration
    
    Contains all settings needed to reproduce the experiments
    described in the paper.
    """
    
    # Dataset configuration
    datasets: List[str] = None
    batch_size: int = 32
    num_workers: int = 4
    reliability_pattern: str = 'dynamic'
    noise_level: float = 0.1
    
    # Framework configuration
    framework_epochs: int = 200
    d_model: int = 512
    n_heads: int = 8
    n_layers: int = 6
    dropout: float = 0.1
    learning_rate: float = 0.001
    
    # Baseline configuration
    baseline_methods: List[str] = None
    baseline_epochs: int = 100
    
    # Evaluation configuration
    n_seeds: int = 5
    confidence_level: float = 0.9
    calibration_size: int = 1000
    
    # Computational configuration
    device: str = 'auto'
    mixed_precision: bool = True
    gradient_clipping: float = 1.0
    
    # Experiment settings
    save_models: bool = True
    save_intermediate: bool = True
    log_wandb: bool = False
    
    def __post_init__(self):
        """Initialize default values"""
        if self.datasets is None:
            self.datasets = [
                'autonomous_vehicle',
                'medical_diagnosis', 
                'environmental_monitoring',
                'financial_markets',
                'iot_networks'
            ]
        
        if self.baseline_methods is None:
            self.baseline_methods = [
                'Kalman Filter',
                'Dempster-Shafer',
                'Weighted Average',
                'PCA Fusion',
                'MLP Fusion',
                'CNN Fusion',
                'LSTM Fusion',
                'Standard Transformer',
                'Cross-Modal Transformer',
                'Multi-Modal BERT',
                'Graph Neural Network',
                'Monte Carlo Dropout',
                'Deep Ensembles',
                'Bayesian Neural Networks',
                'Evidential Learning'
            ]
    
    def save(self, filepath: str):
        """Save configuration to JSON file"""
        config_dict = asdict(self)
        with open(filepath, 'w') as f:
            json.dump(config_dict, f, indent=2)
        logger.info(f"Configuration saved to {filepath}")
    
    @classmethod
    def load(cls, filepath: str) -> 'ExperimentConfig':
        """Load configuration from JSON file"""
        with open(filepath, 'r') as f:
            config_dict = json.load(f)
        
        config = cls(**config_dict)
        logger.info(f"Configuration loaded from {filepath}")
        return config
    
    def update(self, **kwargs):
        """Update configuration parameters"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logger.warning(f"Unknown configuration parameter: {key}")
    
    def get_framework_config(self) -> Dict[str, Any]:
        """Get framework-specific configuration"""
        return {
            'd_model': self.d_model,
            'n_heads': self.n_heads,
            'n_layers': self.n_layers,
            'dropout': self.dropout,
            'learning_rate': self.learning_rate,
            'batch_size': self.batch_size,
            'total_epochs': self.framework_epochs,
            'device': self.device,
            'mixed_precision': self.mixed_precision,
            'gradient_clipping': self.gradient_clipping
        }
    
    def get_data_config(self) -> Dict[str, Any]:
        """Get data-specific configuration"""
        return {
            'datasets': self.datasets,
            'batch_size': self.batch_size,
            'num_workers': self.num_workers,
            'reliability_pattern': self.reliability_pattern,
            'noise_level': self.noise_level
        }
    
    def get_evaluation_config(self) -> Dict[str, Any]:
        """Get evaluation-specific configuration"""
        return {
            'n_seeds': self.n_seeds,
            'confidence_level': self.confidence_level,
            'calibration_size': self.calibration_size,
            'baseline_methods': self.baseline_methods
        }


class ConfigManager:
    """
    Configuration Manager
    
    Manages different experimental configurations for various
    scenarios (quick test, full paper reproduction, ablation studies, etc.)
    """
    
    @staticmethod
    def get_quick_test_config() -> ExperimentConfig:
        """Configuration for quick testing (reduced scale)"""
        return ExperimentConfig(
            datasets=['autonomous_vehicle', 'medical_diagnosis'],
            baseline_methods=['MLP Fusion', 'Standard Transformer', 'Monte Carlo Dropout'],
            framework_epochs=50,
            baseline_epochs=30,
            n_seeds=3,
            batch_size=16,
            d_model=256,
            n_layers=3
        )
    
    @staticmethod
    def get_paper_reproduction_config() -> ExperimentConfig:
        """Configuration for full paper reproduction"""
        return ExperimentConfig(
            datasets=[
                'autonomous_vehicle',
                'medical_diagnosis',
                'environmental_monitoring', 
                'financial_markets',
                'iot_networks',
                'weather_forecasting',
                'traffic_management',
                'energy_systems'
            ],
            framework_epochs=200,
            baseline_epochs=100,
            n_seeds=5,
            batch_size=32,
            d_model=512,
            n_layers=6,
            save_models=True,
            save_intermediate=True
        )
    
    @staticmethod
    def get_ablation_study_config() -> ExperimentConfig:
        """Configuration for ablation studies"""
        return ExperimentConfig(
            datasets=['autonomous_vehicle', 'medical_diagnosis', 'financial_markets'],
            baseline_methods=[],  # No baselines needed for ablation
            framework_epochs=100,
            n_seeds=3,
            batch_size=32,
            d_model=512,
            n_layers=6
        )
    
    @staticmethod
    def get_robustness_test_config() -> ExperimentConfig:
        """Configuration for robustness testing"""
        return ExperimentConfig(
            datasets=['autonomous_vehicle'],  # Single dataset for robustness
            baseline_methods=['MLP Fusion', 'Standard Transformer'],  # Key baselines
            framework_epochs=100,
            baseline_epochs=50,
            n_seeds=3,
            batch_size=32
        )
    
    @staticmethod
    def get_computational_efficiency_config() -> ExperimentConfig:
        """Configuration for computational efficiency analysis"""
        return ExperimentConfig(
            datasets=['autonomous_vehicle'],
            baseline_methods=[
                'MLP Fusion',
                'Standard Transformer', 
                'Deep Ensembles',
                'Bayesian Neural Networks'
            ],
            framework_epochs=50,
            baseline_epochs=30,
            n_seeds=1,  # Single run for timing
            batch_size=32
        )
    
    @staticmethod
    def create_custom_config(
        experiment_type: str = 'full',
        datasets: Optional[List[str]] = None,
        baselines: Optional[List[str]] = None,
        **kwargs
    ) -> ExperimentConfig:
        """Create custom configuration"""
        
        if experiment_type == 'quick':
            config = ConfigManager.get_quick_test_config()
        elif experiment_type == 'full':
            config = ConfigManager.get_paper_reproduction_config()
        elif experiment_type == 'ablation':
            config = ConfigManager.get_ablation_study_config()
        elif experiment_type == 'robustness':
            config = ConfigManager.get_robustness_test_config()
        elif experiment_type == 'efficiency':
            config = ConfigManager.get_computational_efficiency_config()
        else:
            config = ExperimentConfig()
        
        # Override with custom parameters
        if datasets is not None:
            config.datasets = datasets
        if baselines is not None:
            config.baseline_methods = baselines
        
        config.update(**kwargs)
        
        return config
    
    @staticmethod
    def validate_config(config: ExperimentConfig) -> bool:
        """Validate configuration parameters"""
        
        valid = True
        
        # Check datasets
        valid_datasets = [
            'autonomous_vehicle', 'medical_diagnosis', 'environmental_monitoring',
            'financial_markets', 'iot_networks', 'weather_forecasting',
            'traffic_management', 'energy_systems'
        ]
        
        for dataset in config.datasets:
            if dataset not in valid_datasets:
                logger.error(f"Invalid dataset: {dataset}")
                valid = False
        
        # Check baseline methods
        valid_baselines = [
            'Kalman Filter', 'Dempster-Shafer', 'Weighted Average', 'PCA Fusion',
            'MLP Fusion', 'CNN Fusion', 'LSTM Fusion', 'Standard Transformer',
            'Cross-Modal Transformer', 'Multi-Modal BERT', 'Graph Neural Network',
            'Monte Carlo Dropout', 'Deep Ensembles', 'Bayesian Neural Networks',
            'Evidential Learning'
        ]
        
        for baseline in config.baseline_methods:
            if baseline not in valid_baselines:
                logger.error(f"Invalid baseline method: {baseline}")
                valid = False
        
        # Check numerical parameters
        if config.batch_size <= 0:
            logger.error("Batch size must be positive")
            valid = False
        
        if config.framework_epochs <= 0:
            logger.error("Framework epochs must be positive")
            valid = False
        
        if config.n_seeds <= 0:
            logger.error("Number of seeds must be positive")
            valid = False
        
        if not 0 < config.confidence_level < 1:
            logger.error("Confidence level must be between 0 and 1")
            valid = False
        
        return valid
    
    @staticmethod
    def get_config_summary(config: ExperimentConfig) -> str:
        """Get human-readable configuration summary"""
        
        summary = []
        summary.append("EXPERIMENT CONFIGURATION SUMMARY")
        summary.append("=" * 50)
        summary.append(f"Datasets: {len(config.datasets)} ({', '.join(config.datasets[:3])}{'...' if len(config.datasets) > 3 else ''})")
        summary.append(f"Baseline methods: {len(config.baseline_methods)}")
        summary.append(f"Framework epochs: {config.framework_epochs}")
        summary.append(f"Baseline epochs: {config.baseline_epochs}")
        summary.append(f"Random seeds: {config.n_seeds}")
        summary.append(f"Batch size: {config.batch_size}")
        summary.append(f"Model size: d_model={config.d_model}, layers={config.n_layers}")
        summary.append(f"Device: {config.device}")
        summary.append(f"Save models: {config.save_models}")
        summary.append(f"Save intermediate: {config.save_intermediate}")
        
        return "\n".join(summary)


# Predefined configurations for easy access
QUICK_TEST_CONFIG = ConfigManager.get_quick_test_config()
PAPER_REPRODUCTION_CONFIG = ConfigManager.get_paper_reproduction_config()
ABLATION_STUDY_CONFIG = ConfigManager.get_ablation_study_config()
ROBUSTNESS_TEST_CONFIG = ConfigManager.get_robustness_test_config()
EFFICIENCY_CONFIG = ConfigManager.get_computational_efficiency_config()
