#!/bin/bash
#BSUB -J trust_calibrated_experiments
#BSUB -o logs/comprehensive_experiments_%J.out
#BSUB -e logs/comprehensive_experiments_%J.err
#BSUB -n 8
#BSUB -R "span[hosts=1]"
#BSUB -R "rusage[mem=32GB]"
#BSUB -gpu "num=1:mode=exclusive_process"
#BSUB -q gpu
#BSUB -W 24:00

# Trust-Calibrated Transformer Comprehensive Experiments
# Reproduces all results from the paper:
# "Trust-Calibrated Transformer for Dynamic Multi-Source Data Fusion with Uncertainty Quantification"

echo "=========================================="
echo "TRUST-CALIBRATED TRANSFORMER EXPERIMENTS"
echo "=========================================="
echo "Job ID: $LSB_JOBID"
echo "Start time: $(date)"
echo "Host: $(hostname)"
echo "Working directory: $(pwd)"

# Load required modules
module load python/3.9
module load cuda/11.8
module load gcc/9.3.0

# Activate virtual environment
source venv/bin/activate

# Verify GPU availability
echo "GPU Information:"
nvidia-smi

# Set environment variables
export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
export OMP_NUM_THREADS=8

# Create necessary directories
mkdir -p logs
mkdir -p experiment_results
mkdir -p trained_models
mkdir -p figures
mkdir -p tables

echo "=========================================="
echo "STARTING COMPREHENSIVE EXPERIMENTS"
echo "=========================================="

# Run comprehensive experiments with paper reproduction configuration
python run_comprehensive_experiments.py \
    --config paper_reproduction \
    --device cuda \
    --results-dir experiment_results \
    --seed 42

# Check if experiments completed successfully
if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "✅ EXPERIMENTS COMPLETED SUCCESSFULLY!"
    echo "=========================================="
    
    # Generate summary statistics
    echo "Generating experiment summary..."
    python -c "
import json
import os
from datetime import datetime

# Load results
results_file = 'experiment_results/complete_results.json'
if os.path.exists(results_file):
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    print('EXPERIMENT SUMMARY:')
    print('=' * 50)
    
    # Main performance results
    if 'main_performance' in results:
        print('Main Performance Results:')
        for dataset, data in results['main_performance'].items():
            if isinstance(data, dict) and 'methods' in data:
                print(f'  {dataset}: {len(data[\"methods\"])} methods tested')
    
    # Ablation study results
    if 'ablation_studies' in results:
        print(f'Ablation Studies: {len(results[\"ablation_studies\"])} datasets')
    
    # Robustness analysis
    if 'robustness_analysis' in results:
        print('Robustness Analysis: Completed')
    
    print('=' * 50)
    print(f'Results saved to: experiment_results/')
    print(f'Tables and figures: experiment_results/paper_outputs/')
else:
    print('No results file found')
"
    
    # List generated files
    echo ""
    echo "Generated Files:"
    echo "----------------"
    find experiment_results -name "*.json" -o -name "*.tex" -o -name "*.pdf" | head -20
    
    echo ""
    echo "Job completed at: $(date)"
    
else
    echo "=========================================="
    echo "❌ EXPERIMENTS FAILED!"
    echo "=========================================="
    echo "Check logs for error details:"
    echo "  - Job output: logs/comprehensive_experiments_${LSB_JOBID}.out"
    echo "  - Job errors: logs/comprehensive_experiments_${LSB_JOBID}.err"
    echo "  - Python log: experiment.log"
    exit 1
fi

echo "=========================================="
echo "JOB COMPLETED"
echo "=========================================="
