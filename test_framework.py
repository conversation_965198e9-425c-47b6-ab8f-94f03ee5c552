#!/usr/bin/env python3
"""
Quick Test Script for Trust-Calibrated Transformer Framework

Tests basic functionality of all major components to ensure
the implementation is working correctly before running full experiments.
"""

import sys
import os
import torch
import numpy as np
import logging
from typing import Dict, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_framework_components():
    """Test all major framework components"""
    
    logger.info("🧪 Testing Trust-Calibrated Transformer Framework Components")
    logger.info("=" * 60)
    
    # Test 1: Framework Initialization
    logger.info("1. Testing Framework Initialization...")
    try:
        from src.framework import TrustCalibratedFramework
        
        framework = TrustCalibratedFramework(
            input_dims=[64, 32, 16],  # 3 sources
            output_dim=2,
            device='cpu'
        )
        
        logger.info("   ✅ Framework initialized successfully")
        logger.info(f"   📊 {framework.summary()}")
        
    except Exception as e:
        logger.error(f"   ❌ Framework initialization failed: {e}")
        return False
    
    # Test 2: Data Pipeline
    logger.info("\n2. Testing Data Pipeline...")
    try:
        from src.data import MultiSourceDataLoader
        
        data_loader = MultiSourceDataLoader(
            dataset_name='autonomous_vehicle',
            batch_size=8
        )
        
        loaders = data_loader.get_data_loaders(['train'])
        dataset_info = data_loader.get_dataset_info()
        
        # Test batch loading
        batch = next(iter(loaders['train']))
        
        logger.info("   ✅ Data pipeline working correctly")
        logger.info(f"   📊 Dataset: {dataset_info['dataset_name']}")
        logger.info(f"   📊 Sources: {dataset_info['n_sources']}")
        logger.info(f"   📊 Batch shape: {[data.shape for data in batch['source_data']]}")
        
    except Exception as e:
        logger.error(f"   ❌ Data pipeline failed: {e}")
        return False
    
    # Test 3: Trust Learning
    logger.info("\n3. Testing Trust Learning...")
    try:
        from src.framework.trust_learning import DynamicTrustLearning
        
        trust_learner = DynamicTrustLearning(n_sources=3, device='cpu')
        
        # Simulate source observations and predictions
        source_obs = torch.randn(3, 10)  # 3 sources, 10 features each
        predictions = torch.randn(3, 2)  # 3 predictions, 2 output dims
        target = torch.randn(2)
        
        # Update trust weights
        trust_weights = trust_learner.update_trust_weights(source_obs, predictions, target)
        
        logger.info("   ✅ Trust learning working correctly")
        logger.info(f"   📊 Trust weights: {trust_weights.numpy()}")
        
    except Exception as e:
        logger.error(f"   ❌ Trust learning failed: {e}")
        return False
    
    # Test 4: Transformer Model
    logger.info("\n4. Testing Trust-Calibrated Transformer...")
    try:
        from src.framework.transformer import TrustCalibratedTransformer
        
        model = TrustCalibratedTransformer(
            input_dims=[64, 32, 16],
            output_dim=2,
            d_model=128,
            n_heads=4,
            n_layers=2,
            device='cpu'
        )
        
        # Test forward pass
        source_data = [torch.randn(4, dim) for dim in [64, 32, 16]]  # batch_size=4
        trust_weights = torch.softmax(torch.randn(3), dim=0)
        
        output = model(source_data, trust_weights)
        
        logger.info("   ✅ Transformer model working correctly")
        logger.info(f"   📊 Output shape: {output['prediction'].shape}")
        logger.info(f"   📊 Has uncertainty: {'aleatoric_uncertainty' in output}")
        
    except Exception as e:
        logger.error(f"   ❌ Transformer model failed: {e}")
        return False
    
    # Test 5: Uncertainty Decomposition
    logger.info("\n5. Testing Uncertainty Decomposition...")
    try:
        from src.framework.uncertainty import UncertaintyDecomposition
        
        uncertainty_module = UncertaintyDecomposition(
            d_model=128,
            output_dim=2,
            device='cpu'
        )
        
        # Test uncertainty computation
        features = torch.randn(4, 128)  # batch_size=4, d_model=128
        predictions = torch.randn(4, 2)
        trust_weights = torch.softmax(torch.randn(3), dim=0)
        
        uncertainty_output = uncertainty_module(features, predictions, trust_weights)
        
        logger.info("   ✅ Uncertainty decomposition working correctly")
        logger.info(f"   📊 Epistemic uncertainty shape: {uncertainty_output['epistemic_uncertainty'].shape}")
        logger.info(f"   📊 Aleatoric uncertainty shape: {uncertainty_output['aleatoric_uncertainty'].shape}")
        logger.info(f"   📊 Trust uncertainty shape: {uncertainty_output['trust_uncertainty'].shape}")
        
    except Exception as e:
        logger.error(f"   ❌ Uncertainty decomposition failed: {e}")
        return False
    
    # Test 6: Conformal Prediction
    logger.info("\n6. Testing Conformal Prediction...")
    try:
        from src.framework.conformal import ConformalPrediction
        
        conformal_predictor = ConformalPrediction(alpha=0.1, device='cpu')
        
        # Add some calibration samples
        for _ in range(10):
            pred = torch.randn(2)
            target = torch.randn(2)
            uncertainty = torch.ones(2) * 0.1
            trust_weights = torch.softmax(torch.randn(3), dim=0)
            
            conformal_predictor.add_calibration_sample(pred, target, uncertainty, trust_weights)
        
        # Calibrate
        conformal_predictor.calibrate()
        
        # Test prediction intervals
        test_pred = torch.randn(4, 2)
        test_uncertainty = torch.ones(4, 2) * 0.1
        test_trust = torch.softmax(torch.randn(3), dim=0)
        
        lower, upper = conformal_predictor.predict_interval(test_pred, test_uncertainty, test_trust)
        
        logger.info("   ✅ Conformal prediction working correctly")
        logger.info(f"   📊 Prediction interval shape: {lower.shape} to {upper.shape}")
        logger.info(f"   📊 Average interval width: {torch.mean(upper - lower).item():.4f}")
        
    except Exception as e:
        logger.error(f"   ❌ Conformal prediction failed: {e}")
        return False
    
    # Test 7: Baseline Methods
    logger.info("\n7. Testing Baseline Methods...")
    try:
        from src.baselines import BaselineEvaluator
        
        baseline_evaluator = BaselineEvaluator(
            input_dims=[64, 32, 16],
            output_dim=2,
            device='cpu'
        )
        
        # Test a few baseline methods
        test_methods = ['MLP Fusion', 'Standard Transformer']
        
        for method_name in test_methods:
            model = baseline_evaluator.baseline_methods[method_name]
            source_data = [torch.randn(4, dim) for dim in [64, 32, 16]]
            
            output = model(source_data)
            
            logger.info(f"   ✅ {method_name}: output shape {output['prediction'].shape}")
        
    except Exception as e:
        logger.error(f"   ❌ Baseline methods failed: {e}")
        return False
    
    # Test 8: Evaluation Metrics
    logger.info("\n8. Testing Evaluation Metrics...")
    try:
        from src.evaluation.metrics import MetricsCalculator
        
        metrics_calc = MetricsCalculator()
        
        # Generate test data
        predictions = torch.randn(100, 2)
        targets = torch.randn(100, 2)
        uncertainties = torch.ones(100, 2) * 0.1
        
        # Compute metrics
        metrics = metrics_calc.compute_all_metrics(
            predictions=predictions,
            targets=targets,
            uncertainties=uncertainties
        )
        
        logger.info("   ✅ Evaluation metrics working correctly")
        logger.info(f"   📊 RMSE: {metrics['regression']['rmse']:.4f}")
        logger.info(f"   📊 ECE: {metrics['uncertainty']['ece']:.4f}")
        
    except Exception as e:
        logger.error(f"   ❌ Evaluation metrics failed: {e}")
        return False
    
    # Test 9: End-to-End Framework
    logger.info("\n9. Testing End-to-End Framework...")
    try:
        # Create small dataset
        source_data = [torch.randn(16, dim) for dim in [64, 32, 16]]
        
        # Test prediction
        output = framework.predict(source_data, return_uncertainty=True, return_intervals=True)
        
        logger.info("   ✅ End-to-end framework working correctly")
        logger.info(f"   📊 Prediction shape: {output['prediction'].shape}")
        logger.info(f"   📊 Has intervals: {'lower_bound' in output and 'upper_bound' in output}")
        logger.info(f"   📊 Trust weights: {output['trust_weights'].numpy()}")
        
    except Exception as e:
        logger.error(f"   ❌ End-to-end framework failed: {e}")
        return False
    
    logger.info("\n" + "=" * 60)
    logger.info("🎉 ALL TESTS PASSED! Framework is ready for experiments.")
    logger.info("=" * 60)
    
    return True


def test_experiment_configuration():
    """Test experiment configuration system"""
    
    logger.info("\n🔧 Testing Experiment Configuration...")
    
    try:
        from src.experiments.config_manager import ConfigManager
        
        # Test different configurations
        configs = {
            'quick_test': ConfigManager.get_quick_test_config(),
            'paper_reproduction': ConfigManager.get_paper_reproduction_config(),
            'ablation': ConfigManager.get_ablation_study_config()
        }
        
        for name, config in configs.items():
            valid = ConfigManager.validate_config(config)
            logger.info(f"   ✅ {name} config: {'Valid' if valid else 'Invalid'}")
        
        logger.info("   ✅ Experiment configuration system working correctly")
        
    except Exception as e:
        logger.error(f"   ❌ Experiment configuration failed: {e}")
        return False
    
    return True


def main():
    """Main test function"""
    
    logger.info("🚀 Starting Trust-Calibrated Transformer Framework Tests")
    logger.info("This will test all major components to ensure everything is working correctly.")
    logger.info("")
    
    # Check PyTorch installation
    logger.info(f"PyTorch version: {torch.__version__}")
    logger.info(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        logger.info(f"CUDA device: {torch.cuda.get_device_name()}")
    logger.info("")
    
    # Run component tests
    success = test_framework_components()
    
    if success:
        # Run configuration tests
        success = test_experiment_configuration()
    
    if success:
        logger.info("\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        logger.info("The framework is ready for full experiments.")
        logger.info("\nNext steps:")
        logger.info("  • Run quick test: python run_comprehensive_experiments.py --config quick_test")
        logger.info("  • Run full experiments: python run_comprehensive_experiments.py --config paper_reproduction")
        logger.info("  • Submit to cluster: bsub < submit_comprehensive_experiments.sh")
    else:
        logger.error("\n❌ SOME TESTS FAILED!")
        logger.error("Please check the error messages above and fix the issues before running experiments.")
        sys.exit(1)


if __name__ == '__main__':
    main()
